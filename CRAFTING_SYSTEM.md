# 🏭 Advanced Crafting & Production System

## Overview

The Advanced Crafting & Production System is a comprehensive manufacturing system that allows players to create complex items through multi-step production chains, queue management, and automation.

## ✨ **Key Features Implemented**

### 🔧 **Core Crafting System**
- **Multi-Slot Crafting**: Up to 4 simultaneous crafting operations
- **Queue Management**: Smart slot allocation and job tracking
- **Progress Tracking**: Real-time progress bars and time remaining
- **Recipe Validation**: Automatic ingredient checking and consumption

### 🎮 **User Interface**
- **Visual Crafting Window**: Modern UI with rounded corners and animations
- **Recipe Browser**: List of available recipes with ingredient requirements
- **Progress Visualization**: Real-time progress bars for each crafting slot
- **Resource Display**: Current resource amounts and availability

### ⚡ **Advanced Features**
- **Instant Completion**: Speed up crafting with premium currency (Cash)
- **Cancellation System**: Cancel jobs with partial ingredient refund
- **Efficiency Bonuses**: Future support for building and research bonuses
- **Automation Ready**: Foundation for auto-crafting systems

## 🎯 **Crafting Recipes**

### **Basic Resources → Advanced Materials**

```lua
-- Motherboard (60 seconds)
Metal (1) → Motherboard (1)

-- Meter (90 seconds)  
Metal (1) + Plastic (1) → Meter (1)

-- PC (180 seconds)
Motherboard (1) + Meter (1) + Metal (1) → PC (1)
```

### **Production Chain Example**
1. **Factories** produce Metal and Plastic
2. **Craft Motherboards** from Metal
3. **Craft Meters** from Metal + Plastic  
4. **Craft PCs** from Motherboard + Meter + Metal
5. **Use PCs** for building upgrades

## 🔧 **Technical Implementation**

### **Shared System (`CraftingSystem.luau`)**
- **Queue Management**: Smart slot allocation and job tracking
- **Recipe Validation**: Ingredient checking and consumption
- **Progress Calculation**: Real-time progress updates
- **Completion Logic**: Item production and inventory management

### **Server System (`ResourceManager.luau`)**
- **Event Handlers**: Start, complete, cancel, speed-up crafting
- **Timer Management**: Background processing of crafting jobs
- **Data Persistence**: Save/load crafting queues
- **Validation**: Server-side security and anti-cheat

### **Client System (`init.client.luau`)**
- **UI Management**: Crafting window and controls
- **Visual Feedback**: Progress bars and notifications
- **Input Handling**: Keyboard shortcuts and button clicks
- **Real-time Updates**: Live progress tracking

## 🎮 **User Experience**

### **Opening the Crafting System**
- **Button**: Click "Craft (C)" button in top bar
- **Keyboard**: Press **C** key to toggle crafting window
- **Visual Feedback**: Smooth slide-in animation

### **Starting a Craft**
1. Open crafting window
2. View available recipes and requirements
3. Click "Start Crafting PC" (example button)
4. Watch progress bar fill up over time
5. Collect completed items

### **Managing Crafting Queue**
- **4 Slots Available**: Craft multiple items simultaneously
- **Progress Tracking**: See time remaining for each job
- **Cancel Option**: Cancel jobs with partial refund
- **Speed Up**: Use Cash to complete instantly

## 🔄 **Crafting Workflow**

### **1. Resource Collection**
```lua
-- Factories produce basic resources
Metal Factory → Metal (every 30s)
Plastic Factory → Plastic (every 35s)
```

### **2. Recipe Selection**
```lua
-- Check available recipes
local recipes = RemoteFunctions.GetAvailableRecipes:InvokeServer()

-- Check max craftable quantity
local maxQuantity = RemoteFunctions.GetMaxCraftableQuantity:InvokeServer("PC")
```

### **3. Crafting Execution**
```lua
-- Start crafting
RemoteEvents.StartCrafting:FireServer("PC", 1, slotId)

-- Monitor progress
local progress = job.Progress -- 0.0 to 1.0
local timeRemaining = CraftingSystem.GetTimeRemaining(job)
```

### **4. Completion**
```lua
-- Complete crafting
RemoteEvents.CompleteCrafting:FireServer(slotId)

-- Receive crafted items
playerData.Resources.PC = playerData.Resources.PC + quantity
```

## 💎 **Premium Features**

### **Speed Up Crafting**
- **Cost**: 1 Cash per minute remaining
- **Instant**: Complete crafting jobs immediately
- **Usage**: `RemoteEvents.SpeedUpCrafting:FireServer(slotId)`

### **Queue Pro Gamepass**
- **Extra Slots**: +2 additional crafting slots (total 6)
- **Efficiency**: Future bonus crafting speed
- **Automation**: Priority queue management

## 📊 **System Statistics**

### **Performance Metrics**
- **Concurrent Jobs**: Up to 4 per player (6 with gamepass)
- **Recipe Complexity**: 3-ingredient maximum
- **Processing Time**: 60-180 seconds per recipe
- **Queue Efficiency**: Smart slot allocation

### **Resource Management**
- **Ingredient Validation**: Real-time availability checking
- **Consumption**: Immediate ingredient deduction
- **Refund System**: 50-100% refund on cancellation
- **Storage**: Unlimited crafted item storage

## 🚀 **Future Enhancements**

### **Planned Features**
- **Auto-Crafting**: Automatic recipe execution
- **Batch Crafting**: Craft multiple quantities
- **Recipe Research**: Unlock advanced recipes
- **Factory Integration**: Direct factory-to-crafting pipelines

### **Advanced Systems**
- **Production Chains**: Multi-step automated manufacturing
- **Quality System**: Different quality levels for items
- **Specialization**: Player-specific crafting bonuses
- **Market Integration**: Sell crafted items to other players

## 🎯 **Integration Points**

### **Building System**
```lua
-- Use crafted items for building upgrades
local upgradeCost = {
    Pieces = 1000,
    PC = 2,        -- Crafted item requirement
    Metal = 5
}
```

### **Mission System**
```lua
-- Repair buildings with crafted items
local repairCost = {
    PC = 1,        -- Advanced repair materials
    Metal = 2,
    Plastic = 1
}
```

### **Economy System**
```lua
-- Sell crafted items in marketplace
MarketplaceManager.CreateListing(player, "PC", quantity, price)
```

## 🔧 **Configuration**

### **Recipe Customization**
```lua
-- Add new recipes in Config.luau
Config.CRAFTING_RECIPES.NEW_ITEM = {
    Ingredients = {Metal = 2, Plastic = 1},
    Time = 120,
    Output = 1
}
```

### **Slot Management**
```lua
-- Adjust crafting slots
local baseSlots = 2
local bonusSlots = hasGamepass(QUEUE_PRO) and 2 or 0
local totalSlots = baseSlots + bonusSlots
```

## 📱 **Mobile Optimization**

- **Touch-Friendly**: Large buttons and touch targets
- **Responsive UI**: Adapts to different screen sizes
- **Gesture Support**: Swipe to navigate recipes
- **Performance**: Optimized for mobile devices

---

The Advanced Crafting & Production System provides a deep, engaging manufacturing experience that forms the backbone of UrbanSim's economy and progression systems! 🎉

## 🎮 **Ready to Test**

The system is fully functional and ready for testing:

1. **Build**: `rojo build -o "UrbanSim.rbxlx"`
2. **Open**: Load in Roblox Studio
3. **Test**: Press **C** to open crafting system
4. **Craft**: Start crafting PC and watch progress!

The crafting system integrates seamlessly with the existing notification system, providing beautiful visual feedback for all crafting operations! ✨
