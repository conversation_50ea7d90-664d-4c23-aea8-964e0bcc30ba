-- 🏗️ Building Models Creator Script
-- Run this script in Roblox Studio to create basic building models

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create BuildingModels folder if it doesn't exist
local buildingModels = ReplicatedStorage:FindFirstChild("BuildingModels")
if not buildingModels then
    buildingModels = Instance.new("Folder")
    buildingModels.Name = "BuildingModels"
    buildingModels.Parent = ReplicatedStorage
end

-- Function to create a basic building model
local function createBuildingModel(name, size, color, material, details)
    -- Check if model already exists
    local existingModel = buildingModels:FindFirstChild(name)
    if existingModel then
        existingModel:Destroy()
    end
    
    -- Create new model
    local model = Instance.new("Model")
    model.Name = name
    
    -- Create main building part
    local mainPart = Instance.new("Part")
    mainPart.Name = "Base"
    mainPart.Size = Vector3.new(size[1], size[2], size[3])
    mainPart.Position = Vector3.new(0, size[2]/2, 0)
    mainPart.Anchored = true
    mainPart.CanCollide = true
    mainPart.Color = color
    mainPart.Material = material
    mainPart.Parent = model
    
    -- Set as primary part
    model.PrimaryPart = mainPart
    
    -- Add details based on building type
    if details then
        details(model, mainPart, size)
    end
    
    -- Parent to BuildingModels
    model.Parent = buildingModels
    
    print("✅ Created building model:", name)
    return model
end

-- Function to add residential details
local function addResidentialDetails(model, mainPart, size)
    -- Add roof
    local roof = Instance.new("Part")
    roof.Name = "Roof"
    roof.Size = Vector3.new(size[1] + 0.5, 1, size[3] + 0.5)
    roof.Position = Vector3.new(0, size[2] + 0.5, 0)
    roof.Anchored = true
    roof.Color = Color3.new(0.5, 0.3, 0.2)
    roof.Material = Enum.Material.Wood
    roof.Parent = model
    
    -- Add door
    local door = Instance.new("Part")
    door.Name = "Door"
    door.Size = Vector3.new(0.2, 3, 1.5)
    door.Position = Vector3.new(size[1]/2 + 0.1, 1.5, 0)
    door.Anchored = true
    door.Color = Color3.new(0.4, 0.2, 0.1)
    door.Material = Enum.Material.Wood
    door.Parent = model
    
    -- Add windows
    for i = 1, 2 do
        local window = Instance.new("Part")
        window.Name = "Window" .. i
        window.Size = Vector3.new(0.1, 1.5, 1.5)
        window.Position = Vector3.new(size[1]/2 + 0.05, size[2]/2, -size[3]/2 + i * 2)
        window.Anchored = true
        window.Color = Color3.new(0.7, 0.9, 1)
        window.Material = Enum.Material.Glass
        window.Transparency = 0.3
        window.Parent = model
    end
end

-- Function to add commercial details
local function addCommercialDetails(model, mainPart, size)
    -- Add glass front
    local glassFront = Instance.new("Part")
    glassFront.Name = "GlassFront"
    glassFront.Size = Vector3.new(0.1, size[2] * 0.8, size[3] * 0.9)
    glassFront.Position = Vector3.new(size[1]/2 + 0.05, size[2] * 0.4, 0)
    glassFront.Anchored = true
    glassFront.Color = Color3.new(0.8, 0.9, 1)
    glassFront.Material = Enum.Material.Glass
    glassFront.Transparency = 0.2
    glassFront.Parent = model
    
    -- Add sign
    local sign = Instance.new("Part")
    sign.Name = "Sign"
    sign.Size = Vector3.new(0.1, 1, size[3] * 0.6)
    sign.Position = Vector3.new(size[1]/2 + 0.1, size[2] * 0.9, 0)
    sign.Anchored = true
    sign.Color = Color3.new(1, 1, 0)
    sign.Material = Enum.Material.Neon
    sign.Parent = model
end

-- Function to add utility details
local function addUtilityDetails(model, mainPart, size)
    -- Add antenna
    local antenna = Instance.new("Part")
    antenna.Name = "Antenna"
    antenna.Size = Vector3.new(0.5, size[2] * 1.5, 0.5)
    antenna.Position = Vector3.new(0, size[2] + size[2] * 0.75, 0)
    antenna.Anchored = true
    antenna.Color = Color3.new(0.8, 0.8, 0.8)
    antenna.Material = Enum.Material.Metal
    antenna.Parent = model
    
    -- Add electrical effect
    local effect = Instance.new("Part")
    effect.Name = "ElectricEffect"
    effect.Size = Vector3.new(1, 1, 1)
    effect.Position = Vector3.new(0, size[2] + size[2] * 1.2, 0)
    effect.Anchored = true
    effect.Color = Color3.new(0, 1, 1)
    effect.Material = Enum.Material.Neon
    effect.Shape = Enum.PartType.Ball
    effect.Parent = model
end

-- Create basic building models
print("🏗️ Creating basic building models...")

-- Residential Buildings
createBuildingModel("HOUSE_SMALL", {4, 3, 4}, Color3.new(0.8, 0.6, 0.4), Enum.Material.Brick, addResidentialDetails)
createBuildingModel("HOUSE_MEDIUM", {6, 4, 6}, Color3.new(0.7, 0.5, 0.3), Enum.Material.Brick, addResidentialDetails)
createBuildingModel("APARTMENT", {8, 8, 8}, Color3.new(0.6, 0.4, 0.2), Enum.Material.Concrete, addResidentialDetails)

-- Commercial Buildings
createBuildingModel("SHOP_SMALL", {4, 3, 6}, Color3.new(0.2, 0.6, 0.8), Enum.Material.Glass, addCommercialDetails)
createBuildingModel("SHOP_MEDIUM", {6, 4, 8}, Color3.new(0.1, 0.5, 0.7), Enum.Material.Glass, addCommercialDetails)
createBuildingModel("SHOP_LARGE", {8, 5, 10}, Color3.new(0.0, 0.4, 0.6), Enum.Material.Glass, addCommercialDetails)

-- Utility Buildings
createBuildingModel("POWER_PLANT", {10, 6, 10}, Color3.new(0.5, 0.5, 0.8), Enum.Material.Metal, addUtilityDetails)
createBuildingModel("WATER_PLANT", {8, 5, 8}, Color3.new(0.3, 0.7, 0.9), Enum.Material.SmoothPlastic, addUtilityDetails)

-- Industrial Buildings
createBuildingModel("FACTORY_SMALL", {6, 4, 8}, Color3.new(0.6, 0.6, 0.6), Enum.Material.Metal)
createBuildingModel("FACTORY_LARGE", {10, 6, 12}, Color3.new(0.5, 0.5, 0.5), Enum.Material.Metal)

-- Service Buildings
createBuildingModel("POLICE_STATION", {6, 4, 6}, Color3.new(0.2, 0.2, 0.8), Enum.Material.SmoothPlastic)
createBuildingModel("FIRE_STATION", {6, 4, 8}, Color3.new(0.8, 0.2, 0.2), Enum.Material.SmoothPlastic)
createBuildingModel("HOSPITAL", {8, 5, 10}, Color3.new(1, 1, 1), Enum.Material.SmoothPlastic)

print("✅ All basic building models created!")
print("📍 Models are located in ReplicatedStorage > BuildingModels")
print("🎮 You can now see 3D previews in the building menu!")

-- Instructions for users
print("\n📋 INSTRUCTIONS:")
print("1. Run this script in Roblox Studio")
print("2. Check ReplicatedStorage > BuildingModels for the created models")
print("3. You can customize these models by editing them in Studio")
print("4. The building menu will automatically use these models for previews")
print("5. If no model is found, the system will create enhanced fallback previews")

print("\n🎨 CUSTOMIZATION:")
print("- Edit the models in ReplicatedStorage > BuildingModels")
print("- Add more details, textures, or decorations")
print("- The building system will automatically use your custom models")
print("- Models should have a PrimaryPart set for best results")
