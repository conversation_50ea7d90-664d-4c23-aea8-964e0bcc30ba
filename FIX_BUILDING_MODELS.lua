-- 🔧 Fix Building Models Script
-- Run this script in Roblox Studio to fix building model detection

local ReplicatedStorage = game:GetService("ReplicatedStorage")

print("🔧 Building Models Fix Script Starting...")
print("=" .. string.rep("=", 50))

-- Function to check and create BuildingModels folder
local function checkBuildingModelsFolder()
    print("\n📁 Checking BuildingModels folder...")
    
    local buildingModels = ReplicatedStorage:FindFirstChild("BuildingModels")
    if buildingModels then
        print("✅ BuildingModels folder found in ReplicatedStorage")
        return buildingModels
    else
        print("❌ BuildingModels folder NOT found")
        print("🔧 Creating BuildingModels folder...")
        
        local newFolder = Instance.new("Folder")
        newFolder.Name = "BuildingModels"
        newFolder.Parent = ReplicatedStorage
        
        print("✅ Created BuildingModels folder in ReplicatedStorage")
        return newFolder
    end
end

-- Function to list all models in ReplicatedStorage
local function listAllModels()
    print("\n📋 Scanning ReplicatedStorage for models...")
    
    local allModels = {}
    
    -- Recursive function to find models
    local function findModels(parent, path)
        for _, child in pairs(parent:GetChildren()) do
            if child:IsA("Model") then
                table.insert(allModels, {
                    model = child,
                    path = path .. "/" .. child.Name,
                    parent = parent
                })
            elseif child:IsA("Folder") then
                findModels(child, path .. "/" .. child.Name)
            end
        end
    end
    
    findModels(ReplicatedStorage, "ReplicatedStorage")
    
    if #allModels > 0 then
        print("🔍 Found", #allModels, "models in ReplicatedStorage:")
        for i, modelInfo in ipairs(allModels) do
            print("  " .. i .. ". " .. modelInfo.model.Name .. " (at " .. modelInfo.path .. ")")
        end
        return allModels
    else
        print("❌ No models found anywhere in ReplicatedStorage")
        return {}
    end
end

-- Function to move models to BuildingModels folder
local function moveModelsToFolder(buildingModels, allModels)
    print("\n🚚 Moving models to BuildingModels folder...")
    
    local movedCount = 0
    
    for _, modelInfo in ipairs(allModels) do
        local model = modelInfo.model
        
        -- Skip if already in BuildingModels folder
        if model.Parent == buildingModels then
            print("⏭️  " .. model.Name .. " already in BuildingModels folder")
        else
            -- Check if a model with the same name already exists
            local existingModel = buildingModels:FindFirstChild(model.Name)
            if existingModel then
                print("⚠️  " .. model.Name .. " already exists in BuildingModels, skipping")
            else
                -- Move the model
                model.Parent = buildingModels
                movedCount = movedCount + 1
                print("✅ Moved " .. model.Name .. " to BuildingModels")
            end
        end
    end
    
    print("📊 Moved", movedCount, "models to BuildingModels folder")
end

-- Function to create sample models if none exist
local function createSampleModels(buildingModels)
    print("\n🏗️ Creating sample building models...")
    
    local sampleBuildings = {
        {name = "HOUSE_SMALL", size = {4, 3, 4}, color = Color3.new(0.8, 0.6, 0.4), material = Enum.Material.Brick},
        {name = "HOUSE_MEDIUM", size = {6, 4, 6}, color = Color3.new(0.7, 0.5, 0.3), material = Enum.Material.Brick},
        {name = "APARTMENT", size = {8, 8, 8}, color = Color3.new(0.6, 0.4, 0.2), material = Enum.Material.Concrete},
        {name = "SHOP_SMALL", size = {4, 3, 6}, color = Color3.new(0.2, 0.6, 0.8), material = Enum.Material.Glass},
        {name = "POWER_PLANT", size = {10, 6, 10}, color = Color3.new(0.5, 0.5, 0.8), material = Enum.Material.Metal},
        {name = "WATER_PLANT", size = {8, 5, 8}, color = Color3.new(0.3, 0.7, 0.9), material = Enum.Material.SmoothPlastic}
    }
    
    local createdCount = 0
    
    for _, building in ipairs(sampleBuildings) do
        -- Check if model already exists
        local existingModel = buildingModels:FindFirstChild(building.name)
        if existingModel then
            print("⏭️  " .. building.name .. " already exists")
        else
            -- Create new model
            local model = Instance.new("Model")
            model.Name = building.name
            
            -- Create main part
            local part = Instance.new("Part")
            part.Name = "Base"
            part.Size = Vector3.new(building.size[1], building.size[2], building.size[3])
            part.Position = Vector3.new(0, building.size[2]/2, 0)
            part.Anchored = true
            part.CanCollide = true
            part.Color = building.color
            part.Material = building.material
            part.Parent = model
            
            -- Set as primary part
            model.PrimaryPart = part
            
            -- Parent to BuildingModels
            model.Parent = buildingModels
            
            createdCount = createdCount + 1
            print("✅ Created " .. building.name)
        end
    end
    
    print("📊 Created", createdCount, "new sample models")
end

-- Function to verify models are working
local function verifyModels(buildingModels)
    print("\n🔍 Verifying building models...")
    
    local modelCount = 0
    local validModels = 0
    
    for _, child in pairs(buildingModels:GetChildren()) do
        if child:IsA("Model") then
            modelCount = modelCount + 1
            
            -- Check if model has parts
            local partCount = 0
            for _, part in pairs(child:GetChildren()) do
                if part:IsA("BasePart") then
                    partCount = partCount + 1
                end
            end
            
            if partCount > 0 then
                validModels = validModels + 1
                print("✅ " .. child.Name .. " (" .. partCount .. " parts)")
            else
                print("⚠️  " .. child.Name .. " (no parts found)")
            end
        end
    end
    
    print("📊 Total models:", modelCount)
    print("📊 Valid models:", validModels)
    
    if validModels > 0 then
        print("🎉 Building models are ready to use!")
    else
        print("❌ No valid building models found")
    end
end

-- Main execution
print("🚀 Starting building models fix process...")

-- Step 1: Check/create BuildingModels folder
local buildingModels = checkBuildingModelsFolder()

-- Step 2: List all existing models
local allModels = listAllModels()

-- Step 3: Move models to BuildingModels folder if any exist elsewhere
if #allModels > 0 then
    moveModelsToFolder(buildingModels, allModels)
end

-- Step 4: Create sample models if folder is empty
local existingModelCount = 0
for _, child in pairs(buildingModels:GetChildren()) do
    if child:IsA("Model") then
        existingModelCount = existingModelCount + 1
    end
end

if existingModelCount == 0 then
    print("\n📦 No models found in BuildingModels folder")
    print("🏗️ Creating sample models...")
    createSampleModels(buildingModels)
else
    print("\n📦 Found", existingModelCount, "existing models in BuildingModels folder")
end

-- Step 5: Verify everything is working
verifyModels(buildingModels)

-- Final instructions
print("\n" .. string.rep("=", 50))
print("🎯 BUILDING MODELS FIX COMPLETE!")
print("=" .. string.rep("=", 50))
print("📍 Location: ReplicatedStorage > BuildingModels")
print("🎮 The building menu should now show 3D previews!")
print("🔧 If you still don't see models, check the console for debug info")
print("\n💡 TIPS:")
print("  - Add your own models to ReplicatedStorage > BuildingModels")
print("  - Name them exactly like the building types in Config.luau")
print("  - Make sure models have a PrimaryPart set")
print("  - Use the debug functions in the building menu for troubleshooting")

-- Test the model loading function
print("\n🧪 Testing model loading...")
local testBuildings = {"HOUSE_SMALL", "SHOP_SMALL", "POWER_PLANT"}
for _, buildingType in ipairs(testBuildings) do
    local model = buildingModels:FindFirstChild(buildingType)
    if model then
        print("✅ " .. buildingType .. " can be loaded")
    else
        print("❌ " .. buildingType .. " not found")
    end
end

print("\n🏁 Script completed successfully!")
print("🎮 Open the building menu to see your 3D models!")
