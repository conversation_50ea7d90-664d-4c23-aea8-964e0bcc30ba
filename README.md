# 🏙️ UrbanSim - City Management Game for Roblox

A comprehensive city management and building game for Roblox, built with Rojo and modern Lua practices.

## 🎮 Game Overview

UrbanSim is a city management game where players:
- Build and manage their own cities
- Manage resources (Water, Electricity, Population)
- Handle disasters and missions from the "Mad Scientist"
- Participate in clan wars and inter-server marketplace
- Progress through XP levels to unlock new buildings

## 🛠️ Development Setup

This project uses [Rojo](https://github.com/rojo-rbx/rojo) for project management.

### Prerequisites
- [Rojo 7.5.1+](https://rojo.space/)
- Roblox Studio
- [Aftman](https://github.com/LPGhatguy/aftman) (optional, for tool management)

### Getting Started

1. **Install tools** (if using <PERSON><PERSON>man):
   ```bash
   aftman install
   ```

2. **Build the place file**:
   ```bash
   rojo build -o "UrbanSim.rbxlx"
   ```

3. **Open in Roblox Studio**:
   - Open `UrbanSim.rbxlx` in Roblox Studio

4. **Start live sync**:
   ```bash
   rojo serve
   ```

5. **Connect in Studio**:
   - In Roblox Studio, go to Plugins → Rojo → Connect
   - Click "Connect" to enable live sync

## 📁 Project Structure

```
src/
├── server/                 # Server-side scripts
│   ├── init.server.luau   # Main server initialization
│   ├── DataManager.luau   # Player data management
│   ├── BuildingManager.luau # Building placement & upgrades
│   ├── ResourceManager.luau # Resource production & crafting
│   └── MissionManager.luau # Disaster missions system
├── client/                # Client-side scripts
│   └── init.client.luau   # Main client initialization & UI
├── shared/                # Shared modules
│   ├── Config.luau        # Game configuration
│   ├── BuildingSystem.luau # Building logic
│   ├── Hello.luau         # Utility functions
│   └── Assets/            # RemoteEvents & RemoteFunctions
└── gui/                   # GUI components
    └── MainFrame.lua      # Main UI frame

assets/
└── models/                # 3D building models
    └── README.md          # Model guidelines
```

## 🎯 Core Features Implemented

### ✅ Completed Systems
- **Data Management**: Robust player data saving/loading
- **Building System**: Placement, upgrades, road connectivity
- **Resource Management**: Production, crafting, consumption
- **Economy**: Multiple currencies (Pieces, Cash, Keys, etc.)
- **Mission System**: Mad Scientist disasters and repairs
- **UI Framework**: Basic game interface
- **Configuration**: Centralized game settings

### 🚧 Planned Features
- Clan system and wars
- Inter-server marketplace
- Advanced UI components
- 3D building models
- Sound effects and music
- Mobile optimization

## 🎮 Gameplay Systems

### Building Types
- **Residential**: Houses, apartments (generate population)
- **Utility**: Power plants, water treatment (provide services)
- **Service**: Police, fire, hospital (area coverage)
- **Industrial**: Factories (produce resources)
- **Special**: City Hall (tax collection)

### Resources & Crafting
- **Basic Resources**: Metal, Plastic, Wood
- **Crafted Items**: Motherboards, Meters, PCs
- **Crafting Chain**: Metal → Motherboard, Metal + Plastic → Meter, etc.

### Currencies
- **Pieces** 💰: Main currency from taxes
- **Cash** 💎: Premium currency (Robux)
- **Keys** 🔑: Mission rewards
- **Diamond Keys** 💠: Clan war rewards
- **Gamma-Coin** 🪙: End-game currency

## 🔧 Configuration

Game settings can be modified in `src/shared/Config.luau`:
- Building costs and stats
- Resource production rates
- XP requirements and rewards
- Mission parameters
- Gamepass IDs

## 🚀 Deployment

1. **Test locally**: Use Rojo serve for development
2. **Build final**: `rojo build -o "UrbanSim.rbxlx"`
3. **Upload to Roblox**: Publish the place file to Roblox
4. **Configure**: Set up proper Gamepass IDs in Config.luau

## 📚 Documentation

For detailed information about specific systems, see:
- [Building System](src/shared/BuildingSystem.luau)
- [Game Configuration](src/shared/Config.luau)
- [Data Management](src/server/DataManager.luau)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational purposes. Please respect Roblox's Terms of Service when using this code.

---

Built with ❤️ using [Rojo](https://rojo.space/) and modern Lua practices.