-- 🧪 Test RemoteEvents Creation Script
-- Run this script in Roblox Studio to verify RemoteEvents are being created properly

local ReplicatedStorage = game:GetService("ReplicatedStorage")

print("🧪 Testing RemoteEvents Creation...")
print("=" .. string.rep("=", 50))

-- Wait for Assets folder
local Assets = ReplicatedStorage:WaitForChild("Assets", 5)
if not Assets then
    warn("❌ Assets folder not found in ReplicatedStorage!")
    return
end

print("✅ Assets folder found")

-- Try to require RemoteEvents module
local success, RemoteEvents = pcall(function()
    local RemoteEventsModule = Assets:WaitForChild("RemoteEvents", 5)
    if RemoteEventsModule then
        return require(RemoteEventsModule)
    else
        error("RemoteEvents module not found")
    end
end)

if not success then
    warn("❌ Failed to load RemoteEvents module:", RemoteEvents)
    return
end

print("✅ RemoteEvents module loaded successfully")

-- Check if RemoteEvents table is valid
if type(RemoteEvents) ~= "table" then
    warn("❌ RemoteEvents is not a table, it's:", type(RemoteEvents))
    return
end

print("✅ RemoteEvents is a valid table")

-- List all RemoteEvents in the module
print("\n📋 RemoteEvents in module:")
local moduleEventCount = 0
for eventName, event in pairs(RemoteEvents) do
    moduleEventCount = moduleEventCount + 1
    print("  " .. moduleEventCount .. ". " .. eventName .. " (" .. typeof(event) .. ")")
end

print("📊 Total events in module:", moduleEventCount)

-- Check specific building-related events
local requiredEvents = {
    "PlaceBuilding",
    "UpgradeBuilding", 
    "RemoveBuilding",
    "BuildingPlaced",
    "BuildingUpgraded",
    "BuildingRemoved",
    "ShowNotification"
}

print("\n🔍 Checking required building events:")
local foundEvents = 0
for _, eventName in ipairs(requiredEvents) do
    if RemoteEvents[eventName] then
        print("  ✅ " .. eventName .. " - Found")
        foundEvents = foundEvents + 1
    else
        print("  ❌ " .. eventName .. " - Missing")
    end
end

print("📊 Found " .. foundEvents .. "/" .. #requiredEvents .. " required events")

-- Check if RemoteEvents are actually created in Assets folder
print("\n🔍 Checking RemoteEvents in Assets folder:")
local assetsEventCount = 0
for _, child in pairs(Assets:GetChildren()) do
    if child:IsA("RemoteEvent") then
        assetsEventCount = assetsEventCount + 1
        print("  " .. assetsEventCount .. ". " .. child.Name .. " (RemoteEvent)")
    end
end

print("📊 Total RemoteEvents in Assets folder:", assetsEventCount)

-- Specific check for RemoveBuilding
print("\n🎯 Specific check for RemoveBuilding:")
local removeBuildingInModule = RemoteEvents.RemoveBuilding
local removeBuildingInAssets = Assets:FindFirstChild("RemoveBuilding")

print("  Module: RemoteEvents.RemoveBuilding =", removeBuildingInModule and "Found" or "Missing")
print("  Assets: Assets.RemoveBuilding =", removeBuildingInAssets and "Found" or "Missing")

if removeBuildingInModule and removeBuildingInAssets then
    print("  🔗 Same object?", removeBuildingInModule == removeBuildingInAssets)
end

-- Test creating RemoveBuilding manually if missing
if not removeBuildingInAssets then
    print("\n🔧 RemoveBuilding missing, creating manually...")
    local newRemoveBuilding = Instance.new("RemoteEvent")
    newRemoveBuilding.Name = "RemoveBuilding"
    newRemoveBuilding.Parent = Assets
    print("✅ Created RemoveBuilding RemoteEvent manually")
    
    -- Verify it exists now
    local verifyRemoveBuilding = Assets:FindFirstChild("RemoveBuilding")
    print("  Verification:", verifyRemoveBuilding and "Success" or "Failed")
end

-- Final summary
print("\n" .. string.rep("=", 50))
print("🎯 SUMMARY:")
print("=" .. string.rep("=", 50))
print("📦 Assets folder: " .. (Assets and "✅ Found" or "❌ Missing"))
print("📋 RemoteEvents module: " .. (success and "✅ Loaded" or "❌ Failed"))
print("📊 Events in module: " .. moduleEventCount)
print("📊 Events in Assets: " .. assetsEventCount)
print("🎯 RemoveBuilding: " .. (removeBuildingInAssets and "✅ Available" or "❌ Missing"))

if foundEvents == #requiredEvents then
    print("🎉 All required building events are available!")
else
    print("⚠️  Missing " .. (#requiredEvents - foundEvents) .. " required events")
end

print("\n💡 SOLUTIONS:")
print("1. If RemoteEvents are missing, run this script to create them")
print("2. Check that RemoteEvents module is being required properly")
print("3. Ensure Assets folder exists in ReplicatedStorage")
print("4. Verify RemoteEvents module returns the correct table")

print("\n🏁 Test completed!")
