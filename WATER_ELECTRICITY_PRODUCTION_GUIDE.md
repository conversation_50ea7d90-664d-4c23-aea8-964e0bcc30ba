# 💧⚡ Water & Electricity Production System - Complete Guide

## ✅ **WATER & ELECTRICITY SYSTEM FULLY FUNCTIONAL**

Your UrbanSim game has a complete water and electricity production system! Here's how it works and how to use it effectively.

---

## 🏭 **ELECTRICITY PRODUCTION BUILDINGS**

### **1. POWER_PLANT (Centrale Électrique)**
- **Cost**: 800 Pieces
- **⚡ Energy Production**: 50 units per hour
- **Size**: 10x6x10 blocks
- **Unlock Level**: 3
- **Best for**: High energy output, reliable power

### **2. SOLAR_PLANT (Centrale Solaire)**
- **Cost**: 1000 Pieces
- **⚡ Energy Production**: 35 units per hour
- **Size**: 12x2x12 blocks
- **Unlock Level**: 7
- **Special**: Eco-friendly (no pollution)
- **Best for**: Clean energy, large plots

### **3. WIND_TURBINE (Éolienne)**
- **Cost**: 400 Pieces
- **⚡ Energy Production**: 15 units per hour
- **Size**: 4x12x4 blocks
- **Unlock Level**: 5
- **Special**: Eco-friendly (no pollution)
- **Best for**: Cheap clean energy, small footprint

---

## 💧 **WATER PRODUCTION BUILDINGS**

### **1. WATER_PLANT (Station d'Eau)**
- **Cost**: 600 Pieces
- **💧 Water Production**: 30 units per hour
- **Size**: 8x5x8 blocks
- **Unlock Level**: 2
- **Best for**: Basic water supply

### **2. WATER_TREATMENT (Station d'Épuration)**
- **Cost**: 700 Pieces
- **💧 Water Production**: 40 units per hour
- **🔄 Water Treatment**: 25 units per hour (bonus)
- **Size**: 10x4x8 blocks
- **Unlock Level**: 6
- **Best for**: Advanced water management, higher output

---

## 🏠 **BUILDINGS THAT CONSUME WATER & ELECTRICITY**

### **Residential Buildings (Population + Consumption):**

| Building | Population | ⚡ Energy | 💧 Water | Cost | Level |
|----------|------------|-----------|----------|------|-------|
| **Small House** | 4 people | 5 units | 3 units | 100 Pieces | 1 |
| **Medium House** | 6 people | 8 units | 5 units | 200 Pieces | 2 |
| **Apartment** | 12 people | 12 units | 7 units | 400 Pieces | 3 |
| **Mansion** | 12 people | 15 units | 8 units | 1000 Pieces | 8 |
| **Skyscraper** | 50 people | 30 units | 20 units | 2000 Pieces | 12 |

---

## ⚙️ **HOW THE SYSTEM WORKS**

### **🔄 Automatic Production & Consumption Tracking:**

```lua
-- Enhanced city statistics tracking
function BuildingManager.UpdateCityStats(player)
    local energyProduction = 0
    local waterProduction = 0
    local energyConsumption = 0
    local waterConsumption = 0
    
    -- Calculate from all buildings
    for _, building in pairs(playerData.Buildings) do
        if building.Active then
            local config = Config.BUILDINGS[building.Type]
            
            -- Production buildings add to supply
            if config.EnergyProduction then
                energyProduction += config.EnergyProduction * building.Level
            end
            if config.WaterProduction then
                waterProduction += config.WaterProduction * building.Level
            end
            
            -- Residential buildings consume resources
            if config.EnergyConsumption then
                energyConsumption += config.EnergyConsumption * building.Level
            end
            if config.WaterConsumption then
                waterConsumption += config.WaterConsumption * building.Level
            end
        end
    end
    
    -- Calculate net production (production - consumption)
    local netEnergyProduction = energyProduction - energyConsumption
    local netWaterProduction = waterProduction - waterConsumption
    
    -- Warn about shortages
    if netEnergyProduction < 0 then
        ShowNotification(player, "Warning", "⚡ Energy shortage! Build more power plants.")
    end
    if netWaterProduction < 0 then
        ShowNotification(player, "Warning", "💧 Water shortage! Build more water plants.")
    end
end
```

### **📊 Resource Balance Examples:**

**Example 1: Small City**
- 3x Small Houses = 12 people, 15 energy, 9 water needed
- 1x Wind Turbine = 15 energy produced
- 1x Water Plant = 30 water produced
- **Result**: ✅ Balanced (0 energy surplus, 21 water surplus)

**Example 2: Growing City**
- 2x Apartments = 24 people, 24 energy, 14 water needed
- 1x Power Plant = 50 energy produced
- 1x Water Treatment = 40 water produced
- **Result**: ✅ Surplus (26 energy surplus, 26 water surplus)

**Example 3: Energy Crisis**
- 1x Skyscraper = 50 people, 30 energy, 20 water needed
- 1x Wind Turbine = 15 energy produced
- 1x Water Plant = 30 water produced
- **Result**: ❌ Energy shortage (-15 energy deficit, 10 water surplus)

---

## 🎯 **STRATEGIC BUILDING TIPS**

### **⚡ Electricity Strategy:**

1. **Early Game (Levels 1-4):**
   - Start with **Wind Turbines** (cheap, eco-friendly)
   - 1 Wind Turbine supports 3 Small Houses

2. **Mid Game (Levels 5-7):**
   - Build **Power Plants** for high output
   - 1 Power Plant supports 10 Small Houses or 4 Apartments

3. **Late Game (Levels 8+):**
   - Use **Solar Plants** for clean energy
   - Mix of all three for optimal efficiency

### **💧 Water Strategy:**

1. **Early Game (Levels 1-3):**
   - Start with **Water Plants**
   - 1 Water Plant supports 10 Small Houses

2. **Late Game (Levels 6+):**
   - Upgrade to **Water Treatment** plants
   - Higher output + water treatment bonus

### **🏗️ Building Order Recommendations:**

**Level 1-2:** Small Houses → Water Plant
**Level 3-4:** Wind Turbine → More Houses
**Level 5-6:** Power Plant → Apartments → Water Treatment
**Level 7+:** Solar Plants → Skyscrapers

---

## 📈 **PRODUCTION SCALING**

### **Building Level Effects:**
- **All production scales with building level**
- Level 2 building = 2x production
- Level 3 building = 3x production
- **Upgrade buildings to increase efficiency!**

### **Efficiency Calculations:**

**Wind Turbine Efficiency:**
- Level 1: 15 energy for 400 pieces = 0.0375 energy/piece
- Level 2: 30 energy for 400 pieces = 0.075 energy/piece
- Level 3: 45 energy for 400 pieces = 0.1125 energy/piece

**Power Plant Efficiency:**
- Level 1: 50 energy for 800 pieces = 0.0625 energy/piece
- Level 2: 100 energy for 800 pieces = 0.125 energy/piece

---

## 🚨 **SHORTAGE WARNINGS**

### **Automatic Shortage Detection:**
The system automatically warns you when:
- **Energy shortage**: ⚡ "Energy shortage! Build more power plants. Deficit: X"
- **Water shortage**: 💧 "Water shortage! Build more water plants. Deficit: X"

### **How to Fix Shortages:**

**Energy Shortage Solutions:**
1. Build more **Power Plants** (fastest solution)
2. Add **Wind Turbines** (cheaper option)
3. Upgrade existing power buildings
4. Reduce consumption by removing some houses

**Water Shortage Solutions:**
1. Build more **Water Plants**
2. Upgrade to **Water Treatment** plants
3. Upgrade existing water buildings
4. Reduce consumption by removing some houses

---

## 🎮 **GAMEPLAY FLOW**

### **1. Plan Your City:**
- Calculate how many houses you want
- Determine energy and water needs
- Plan production buildings accordingly

### **2. Build Production First:**
- Always build power and water plants BEFORE houses
- Ensure surplus production for growth

### **3. Monitor Balance:**
- Check notifications for shortage warnings
- Upgrade buildings when needed
- Expand production before adding more houses

### **4. Optimize Efficiency:**
- Upgrade buildings to higher levels
- Use eco-friendly options when available
- Balance cost vs. output

---

## 🎊 **RESULT**

✅ **Complete water and electricity production system**
✅ **Automatic consumption tracking and balance calculation**
✅ **Shortage warnings and notifications**
✅ **Scalable production with building levels**
✅ **Strategic building options for different play styles**

### **Key Features:**
- **Real-time resource tracking**: Production and consumption calculated automatically
- **Smart notifications**: Warnings when shortages occur
- **Building variety**: Multiple options for different strategies
- **Level scaling**: Buildings become more efficient when upgraded
- **Eco-friendly options**: Solar and wind power for clean energy

### **Strategic Depth:**
- **Resource management**: Balance production and consumption
- **Economic planning**: Choose cost-effective building combinations
- **City growth**: Scale production to support population growth
- **Efficiency optimization**: Upgrade buildings for better output

Your water and electricity system provides **complete resource management** with strategic depth, automatic tracking, and clear feedback to help players build successful cities! 💧⚡🏙️✨

## 🔧 **TROUBLESHOOTING**

### **If production isn't working:**
1. **Check building placement** - Ensure buildings are properly placed
2. **Verify building levels** - Higher levels = more production
3. **Check building activity** - Ensure buildings are marked as Active

### **If shortages persist:**
1. **Count your buildings** - Verify production vs consumption
2. **Check building configs** - Ensure Config.BUILDINGS has correct values
3. **Monitor notifications** - System will tell you exact deficit amounts

The system provides excellent feedback and debugging capabilities for all resource management scenarios!
