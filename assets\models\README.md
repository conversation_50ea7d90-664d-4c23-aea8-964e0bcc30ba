# Building Models

This folder contains all the 3D models for buildings in UrbanSim.

## Structure

- `Residential/` - Houses, apartments, etc.
- `Commercial/` - Shops, offices, etc.
- `Industrial/` - Factories, warehouses, etc.
- `Utility/` - Power plants, water treatment, etc.
- `Service/` - Police, fire, hospital, etc.
- `Decoration/` - Parks, monuments, etc.
- `Special/` - City Hall, unique buildings, etc.

## Model Requirements

Each building model should:
1. Be properly scaled (1 stud = 1 meter)
2. Have a primary part named "Base"
3. Include a "BuildingInfo" StringValue with metadata
4. Be optimized for performance (low poly count)
5. Use appropriate materials and colors

## Naming Convention

Models should be named using the format: `BuildingType_VariantName`

Examples:
- `House_Small`
- `PowerPlant_Coal`
- `Police_Station`
