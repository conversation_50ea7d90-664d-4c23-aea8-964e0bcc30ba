🏙️ UrbanSim – Game Design Document (GDD)
1. Vision & Concept
UrbanSim est un jeu de gestion et de construction de ville sur Roblox. Le joueur endosse le rôle d'un maire, chargé de transformer un terrain vierge en une métropole prospère et futuriste. Le cœur du jeu réside dans la gestion stratégique des ressources, des services et de l'espace, où chaque décision a un impact direct sur la croissance de la ville et le bonheur de ses habitants.
L'objectif est de créer une expérience de jeu profonde mais accessible, combinant la satisfaction de la construction créative avec des défis de gestion stimulants, des interactions sociales via les clans et un système de progression à long terme.
2. Core Gameplay Loop
Le cycle de jeu principal est conçu pour être engageant et gratifiant :
Collecter : Le joueur perçoit des impôts (Pièces) de ses habitants via l'Hôtel de Ville.
Produire : Les usines transforment des matières premières en ressources de base (Métal, Plastique, etc.).
Crafter : Les ressources de base sont combinées pour créer des matériaux de construction plus complexes (Cartes Mères, Mètres, etc.).
Construire & Améliorer : Le joueur utilise les Pièces et les matériaux pour construire de nouveaux bâtiments ou améliorer les existants, ce qui augmente la population et débloque de nouvelles fonctionnalités.
Gérer : Le joueur doit équilibrer la production et la consommation des services vitaux (Eau, Électricité) et assurer une couverture adéquate des services d'urgence.
Étendre : En accomplissant des mini-quêtes (bulles de dialogue), le joueur obtient des objets spéciaux pour débloquer de nouvelles parcelles de terrain.
3. Systèmes de Jeu Détaillés
🏗️ Construction & Placement
Connexion Routière : Tous les bâtiments doivent être connectés à une route pour être fonctionnels. Le système doit aider le joueur à aligner les bâtiments correctement.
Expansion de Terrain : La ville débute sur un terrain limité. De nouvelles zones peuvent être débloquées en utilisant des Objets Spéciaux (ex: Engrenage, Casque de chantier, Bouton) obtenus via les bulles de dialogue des habitants. Le coût d'expansion augmente progressivement.
⚡ Services Vitaux
Production/Consommation : L'Eau et l'Électricité sont produites par des bâtiments dédiés. La production totale du réseau doit être supérieure à la consommation totale de la ville. Un déficit entraîne des pannes et une perte d'habitants.
Couverture de Zone : La Police, les Pompiers, les Hôpitaux, les Éboueurs et les Stations d'eau usée fonctionnent via une zone de couverture (implémentée avec Region3 ou des calculs de magnitude). Les habitations non couvertes ne génèrent pas d'habitants et peuvent être sujettes à des problèmes (criminalité, incendies, etc.).
🌳 Services de Confort
Boost de Population : Les Parcs, Écoles, Casinos, Centres sportifs, etc., ne sont pas obligatoires mais fournissent un bonus de population aux habitations situées dans leur rayon d'effet.
Gamepass : Un Gamepass Boost Service x2 double l'efficacité de ces bâtiments.
🧱 Amélioration de Bâtiments
Prérequis : Nécessite des ressources craftées (Métal, Plastique, PC, etc.).
Progression : Chaque amélioration augmente la capacité d'un bâtiment (plus d'habitants, plus de production, etc.) et rapporte de l'XP au joueur.
Acquisition : Les matériaux peuvent être achetés au Shop Inter-Serveur avec des Pièces si le joueur ne veut pas attendre le craft.
🤖 Crafting & Ressources
Production : Les usines produisent des ressources de base (Métal, Plastique, Bois, etc.).
Assemblage : Des bâtiments spécialisés permettent de combiner ces ressources pour créer des objets complexes.
Exemple : Métal + Plastique → Mètre. Métal → Carte Mère. Mètre + Carte Mère → PC.
Temps d'Attente : Chaque craft a une durée, qui peut être accélérée avec la monnaie premium (Cash).
4. Économie & Monnaies
Nom	Icône	Usage & Obtention
Pièces	💰	Monnaie principale. Obtenue via les impôts (Hôtel de Ville). Utilisée pour construire, améliorer, et acheter au Shop Inter-Serveur.
Cash	💎	Monnaie Premium. Achetée avec des Robux. Utilisée pour accélérer les timers, acheter des objets spéciaux et des packs.
Clés	🔑	Obtenues en récompense des missions du "Scientifique Fou". Utilisées pour ouvrir des coffres spéciaux.
Clés Diamant	💠	Obtenues lors des Guerres de Clans. Utilisées pour des récompenses de clan exclusives (bâtiments, boosts).
Gamma-Coin	🪙	Monnaie de fin de jeu, pour les bâtiments futuristes. Produite par des bâtiments spéciaux et stockée dans une banque dédiée.
📦 Stockage
Le stock de Pièces et de Cash est illimité.
Le stock des ressources de craft est limité par la capacité de l'Entrepôt, qui peut être amélioré.
Le stock de Gamma-Coin est limité par défaut et peut être augmenté via un bâtiment de stockage spécifique.
5. Fonctionnalités Sociales & Événements
🌐 Shop Inter-Serveur
Place de marché où tous les joueurs peuvent poster des offres de vente et d'achat pour les ressources de craft.
Transactions effectuées en Pièces.
Permet de spécialiser sa production et de combler ses manques en interagissant avec l'économie globale du jeu.
🔥 Missions : Le Scientifique Fou
Un PNJ scientifique déclenche périodiquement des catastrophes sur la ville du joueur (tornade, incendie, invasion de robots, etc.).
Les bâtiments touchés sont endommagés et cessent de fonctionner.
Le joueur doit utiliser des ressources spécifiques pour les réparer.
Récompenses : Clés, XP, et ressources rares.
🛡️ Clans & Guerres de Ville
Les joueurs peuvent créer ou rejoindre des clans.
Les Guerres de Ville sont des événements PvP asynchrones entre clans, basés sur des défis de production ou de missions.
Récompenses : Clés Diamant, boosts de production temporaires, et trophées pour le clan.
6. Interface Utilisateur (UI/UX)
Vue Principale : Affichage des stats principales (monnaies, population, XP), boutons d'accès aux menus principaux.
Menu de Construction : Interface basée sur des onglets (Habitations, Services, Production, Décorations) pour une navigation facile.
Fenêtre d'Interaction : Clic sur un bâtiment pour voir son statut, l'améliorer ou collecter ses ressources.
Bulles de Dialogue : Les habitants auront des bulles de pensée/dialogue, cliquant dessus pourra rapporter des objets spéciaux pour l'expansion.
Hôtel de Ville : Interface pour collecter les impôts journaliers, dont le montant est basé sur la population totale.
7. Monetization (Gamepasses)
Gamepass	Effet
Argent x2	💰 Double les Pièces collectées à l'Hôtel de Ville.
Boost Service x2	🌟 Double le bonus de population des services de confort (parcs, etc.).
File d'Attente Pro	⚙️ Ajoute des slots supplémentaires dans les usines de crafting.
Construction Pro	🧰 Accès anticipé à certains bâtiments et décorations exclusives.
Pack Premium	💎 Offre unique : une grande quantité de Cash, un bâtiment rare, et un pack de ressources.
Gamma-Coin x2	🪙 (Plus tard) Double la production de Gamma-Coin des bâtiments futuristes.
8. Structure Technique du Projet (Roblox)
ServerScriptService
ServerScripts/
DataManager.lua: Gère la sauvegarde et le chargement des données joueurs avec ProfileService ou un wrapper DataStore robuste.
PlayerManager.lua: Gère la logique à la connexion/déconnexion du joueur.
BuildingManager.lua: Logique serveur pour le placement, l'amélioration et la destruction des bâtiments.
ResourceManager.lua: Gère la production, le crafting, et la consommation de toutes les ressources et services (énergie, eau, etc.).
MissionManager.lua: Déclenche et gère les catastrophes.
ClanManager.lua: Gère la création, la gestion et les guerres de clans.
MarketplaceManager.lua: Gère la logique du Shop Inter-Serveur.
ReplicatedStorage
Assets/: Contient les RemoteEvents et RemoteFunctions.
Shared/
Config.lua: Table de configuration centrale (coûts, temps de craft, stats des bâtiments, XP par niveau).
BuildingSystem.lua: Module partagé pour les règles de placement (valide côté client et vérifié côté serveur).
Modules/: Modules de code partagés (ex: gestionnaire de UI, calcul de zone d'effet).
ServerStorage
Models/: Tous les modèles de bâtiments, routes, et décorations. Ils seront clonés, jamais créés avec Instance.new.
StarterPlayer
StarterPlayerScripts/
Client.lua: Script principal côté client, qui initialise tous les autres modules clients.
StarterCharacterScripts/
StarterGui/
MainGui: Contient tous les éléments de l'interface, gérés par un UIManager côté client.
9. Pour Commencer (MVP - Minimum Viable Product)
Pour lancer le projet, la priorité est de développer le Core Gameplay Loop.
Placement & Routes : Un système fonctionnel pour placer des bâtiments et des routes.
Habitations & Population : Construire une maison ajoute de la population.
Hôtel de Ville & Pièces : Collecter des pièces basées sur la population.
Énergie & Eau : Système basique de production/consommation. Les maisons nécessitent de l'énergie pour générer des habitants.
Sauvegarde des Données : Implémenter un système de sauvegarde fiable pour la position des bâtiments et les stats du joueur.