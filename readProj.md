🎯 Résumé
tis is a roblox project for roblox game lua etc . UrbanSim est un jeu de gestion de ville où tu construis, améliores et étends ta métropole. Chaque bâtiment dépend des routes, services et ressources. Gère l'énergie, l’eau, les services d’urgence, le bonheur des habitants, les catastrophes, les crafts, et participe à des événements interserveurs et guerres de clans. Progresse via de l’XP pour débloquer de nouveaux services et bâtiments futuristes.

🛠️ SYSTÈMES DE JEU
🏗️ Placement
Tous les bâtiments doivent être placés connectés à une route.

La ville commence avec un terrain limité → débloque de nouvelles zones avec des objets spéciaux.

⚡ Services Vitaux
Eau, Électricité → produits en quantités mesurées. La ville doit produire plus que ce qu’elle consomme.

Police, Pompiers, Hôpital, Éboueurs, Station d’eau usée → fonctionnement par zone de couverture (via Region3).

Sans services → pas d’habitants dans les maisons.

🌳 Services de Confort (boost population)
Parcs, Écoles, Casinos, Centres sportifs, Plages, Montagnes.

Apportent un bonus d’habitants par bâtiment proche.

Boost x2 disponible via Gamepass.

🧱 Amélioration de Bâtiments
Nécessite des ressources (Métal, Plastique, etc.)

Possibilité d’acheter les matériaux au Shop Inter-Serveur avec pièces

Chaque amélioration rapporte de l’XP joueur

L’XP débloque de nouveaux services (ex. École, Casino)

🌐 SHOP INTERSERVEUR
Accessible à tous les joueurs.

Permet :

Vendre des matériaux que tu produits

Acheter des matériaux que tu n’as pas pour les améliorations

Fonctionne avec la monnaie pièces

💸 MONNAIES & ÉCONOMIE
Nom	Usage
Pièces	Bâtiments, matériaux au shop interserveur
Cash (Premium)	Accélérer les crafts / acheter items spéciaux
Clés	Obtenues via missions (scientifique fou)
Clés Diamant	Obtenues via guerres de ville (clan)
Gamma-Coin (Futur)	Monnaie exclusive des bâtiments futuristes

📦 Stockage
Stock de Gamma-Coin limité par défaut → peut être amélioré

🔥 MISSIONS : Le Scientifique Fou
Envoie régulièrement des catastrophes naturelles

Exemples : tornade, incendie, tremblement de terre

Le joueur doit réparer les dégâts avec des ressources

Récompenses : Clés, XP, ressources rares

🎁 DAILY CASH (Argent journalier)
Récoltable dans l’Hôtel de Ville

Montant basé sur le nombre d’habitants

🧠 XP & DÉBLOCAGE
Chaque amélioration de bâtiment donne de l’XP

XP = système de progression du joueur

Débloque : services avancés, bâtiments spéciaux, etc.

⚙️ OBJETS SPÉCIAUX (pour débloquer des zones)
Obtenus via bulles de dialogues des habitants

Exemples :

Engrenage

Casque de chantier

Bouton

Chaque zone coûte de plus en plus cher

🤖 CRAFTING & RESSOURCES
Usines produisent : Métal, Plastique, etc.

Exemples :

Carte Mère → Métal

Mètre → Plastique + Métal

PC → Carte Mère + Mètre + Métal

Chaque craft = temps d’attente (accélérable avec Cash)

🛡️ CLANS & GUERRES DE VILLE
Forme ou rejoins un clan

Guerres de ville = événements PvP entre clans

Récompenses :

Clés Diamant

Boosts de production

Trophées

🎮 GAMEPASSES
Nom	Effet
💰 Argent x2	Double les revenus
🌟 Boost Service x2	Multiplie les boosts des parcs etc.
⚡ Skip Temps	Accélère les crafts
🧰 Construction Pro	Accès anticipé à certains bâtiments
💎 Pack Premium	Monnaie Cash, bâtiment rare, ressources
🪙 Gamma-Coin x2	Multiplie les revenus futuristes (plus tard)

📈 LEADERSTATS À SAUVEGARDER
Pieces

Cash

Cles

ClesDiamant

GammaCoin

XP

Population

ProductionEnergie

ProductionEau

🧩 MODULES/SCRIPTS À DÉVELOPPER
Placement de bâtiments + alignement aux routes

Système de consommation/production électricité/eau

Zone de couverture (Region3) des services

Générateur de ressources + usine

Crafting d’objets

Système de dialogue des habitants

Missions avec dégâts + réparations

XP + déblocage services

Déblocage zones via objets spéciaux

Interface Hôtel de ville + argent journalier

Système de clan + guerre

Shop interserveur (achat/vente matériaux entre joueurs)

Stockage gamma-coin amélioré