--[[
	Building Debugger
	Debug tools for building placement issues
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

-- Get shared modules
local Shared = ReplicatedStorage:WaitForChild("Shared")
local Config = require(Shared:WaitForChild("Config"))
local BuildingSystem = require(Shared:WaitForChild("BuildingSystem"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))
local RemoteFunctions = require(Assets:WaitForChild("RemoteFunctions"))

local BuildingDebugger = {}

-- Debug state
local debugMode = false
local debugGui = nil

-- Create debug GUI
function BuildingDebugger.CreateDebugGUI()
	local playerGui = player:WaitFor<PERSON>hild("PlayerGui")
	
	debugGui = Instance.new("ScreenGui")
	debugGui.Name = "BuildingDebugger"
	debugGui.Parent = playerGui
	
	-- Debug panel
	local debugPanel = Instance.new("Frame")
	debugPanel.Name = "DebugPanel"
	debugPanel.Size = UDim2.new(0, 300, 0, 400)
	debugPanel.Position = UDim2.new(0, 10, 0, 10)
	debugPanel.BackgroundColor3 = Color3.new(0, 0, 0)
	debugPanel.BackgroundTransparency = 0.3
	debugPanel.BorderSizePixel = 0
	debugPanel.Visible = false
	debugPanel.Parent = debugGui
	
	-- Corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = debugPanel
	
	-- Title
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, -20, 0, 30)
	title.Position = UDim2.new(0, 10, 0, 10)
	title.BackgroundTransparency = 1
	title.Text = "🔧 Building Debugger"
	title.TextColor3 = Color3.new(1, 1, 1)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.TextXAlignment = Enum.TextXAlignment.Left
	title.Parent = debugPanel
	
	-- Debug info
	local debugInfo = Instance.new("TextLabel")
	debugInfo.Name = "DebugInfo"
	debugInfo.Size = UDim2.new(1, -20, 1, -50)
	debugInfo.Position = UDim2.new(0, 10, 0, 40)
	debugInfo.BackgroundTransparency = 1
	debugInfo.Text = "Debug info will appear here..."
	debugInfo.TextColor3 = Color3.new(1, 1, 1)
	debugInfo.TextScaled = false
	debugInfo.TextSize = 12
	debugInfo.Font = Enum.Font.Code
	debugInfo.TextXAlignment = Enum.TextXAlignment.Left
	debugInfo.TextYAlignment = Enum.TextYAlignment.Top
	debugInfo.TextWrapped = true
	debugInfo.Parent = debugPanel
	
	return debugPanel
end

-- Toggle debug mode
function BuildingDebugger.ToggleDebug()
	debugMode = not debugMode
	
	if not debugGui then
		BuildingDebugger.CreateDebugGUI()
	end
	
	local debugPanel = debugGui:FindFirstChild("DebugPanel")
	if debugPanel then
		debugPanel.Visible = debugMode
	end
	
	print("Building Debug Mode:", debugMode and "ON" or "OFF")
end

-- Update debug info
function BuildingDebugger.UpdateDebugInfo(info)
	if not debugMode or not debugGui then return end
	
	local debugPanel = debugGui:FindFirstChild("DebugPanel")
	local debugInfo = debugPanel and debugPanel:FindFirstChild("DebugInfo")
	
	if debugInfo then
		debugInfo.Text = info
	end
end

-- Test building placement
function BuildingDebugger.TestBuildingPlacement(buildingType, position)
	local debugText = "🔧 BUILDING PLACEMENT DEBUG\n\n"
	
	-- Test 1: Check building type
	debugText = debugText .. "1. Building Type: " .. tostring(buildingType) .. "\n"
	local buildingConfig = Config.BUILDINGS[buildingType]
	if buildingConfig then
		debugText = debugText .. "   ✅ Building config found\n"
		debugText = debugText .. "   Size: " .. table.concat(buildingConfig.Size, "x") .. "\n"
		debugText = debugText .. "   Cost: " .. tostring(buildingConfig.Cost.Pieces or 0) .. " pieces\n"
	else
		debugText = debugText .. "   ❌ Building config NOT found\n"
		BuildingDebugger.UpdateDebugInfo(debugText)
		return false
	end
	
	-- Test 2: Check position
	debugText = debugText .. "\n2. Position: " .. tostring(position) .. "\n"
	local gridPosition = BuildingSystem.WorldToGrid(position)
	local worldPosition = BuildingSystem.GridToWorld(gridPosition)
	debugText = debugText .. "   Grid: " .. tostring(gridPosition) .. "\n"
	debugText = debugText .. "   World: " .. tostring(worldPosition) .. "\n"
	
	-- Test 3: Check RemoteFunction
	debugText = debugText .. "\n3. Testing CanPlaceBuilding RemoteFunction...\n"
	local success, canPlace = pcall(function()
		return RemoteFunctions.CanPlaceBuilding:InvokeServer(buildingType, worldPosition)
	end)
	
	if success then
		debugText = debugText .. "   ✅ RemoteFunction call successful\n"
		debugText = debugText .. "   Can Place: " .. tostring(canPlace) .. "\n"
	else
		debugText = debugText .. "   ❌ RemoteFunction call failed: " .. tostring(canPlace) .. "\n"
		BuildingDebugger.UpdateDebugInfo(debugText)
		return false
	end
	
	-- Test 4: Test placement
	if canPlace then
		debugText = debugText .. "\n4. Testing PlaceBuilding RemoteEvent...\n"
		local placeSuccess, placeResult = pcall(function()
			RemoteEvents.PlaceBuilding:FireServer(buildingType, worldPosition)
		end)
		
		if placeSuccess then
			debugText = debugText .. "   ✅ PlaceBuilding call successful\n"
		else
			debugText = debugText .. "   ❌ PlaceBuilding call failed: " .. tostring(placeResult) .. "\n"
		end
	else
		debugText = debugText .. "\n4. ❌ Cannot place building at this location\n"
	end
	
	BuildingDebugger.UpdateDebugInfo(debugText)
	return canPlace
end

-- Test all building types
function BuildingDebugger.TestAllBuildings()
	local debugText = "🔧 TESTING ALL BUILDING TYPES\n\n"
	
	for buildingType, config in pairs(Config.BUILDINGS) do
		debugText = debugText .. buildingType .. ": "
		if config.Size and config.Cost then
			debugText = debugText .. "✅ Valid\n"
		else
			debugText = debugText .. "❌ Invalid config\n"
		end
	end
	
	BuildingDebugger.UpdateDebugInfo(debugText)
end

-- Test RemoteEvents connectivity
function BuildingDebugger.TestRemoteEvents()
	local debugText = "🔧 TESTING REMOTE EVENTS\n\n"
	
	-- Test RemoteEvents
	debugText = debugText .. "RemoteEvents:\n"
	if RemoteEvents.PlaceBuilding then
		debugText = debugText .. "  ✅ PlaceBuilding found\n"
	else
		debugText = debugText .. "  ❌ PlaceBuilding missing\n"
	end
	
	-- Test RemoteFunctions
	debugText = debugText .. "\nRemoteFunctions:\n"
	if RemoteFunctions.CanPlaceBuilding then
		debugText = debugText .. "  ✅ CanPlaceBuilding found\n"
	else
		debugText = debugText .. "  ❌ CanPlaceBuilding missing\n"
	end
	
	-- Test server connectivity
	debugText = debugText .. "\nServer Connectivity:\n"
	local success, result = pcall(function()
		return RemoteFunctions.GetPlayerData:InvokeServer()
	end)
	
	if success then
		debugText = debugText .. "  ✅ Server connection working\n"
	else
		debugText = debugText .. "  ❌ Server connection failed: " .. tostring(result) .. "\n"
	end
	
	BuildingDebugger.UpdateDebugInfo(debugText)
end

-- Initialize debugger
function BuildingDebugger.Initialize()
	-- Create debug commands
	local function onChatted(message)
		local args = string.split(message, " ")
		local command = args[1]:lower()
		
		if command == "/debug" then
			BuildingDebugger.ToggleDebug()
		elseif command == "/testbuilding" then
			local buildingType = args[2] or "HOUSE_SMALL"
			local mouse = player:GetMouse()
			if mouse.Hit then
				BuildingDebugger.TestBuildingPlacement(buildingType, mouse.Hit.Position)
			end
		elseif command == "/testall" then
			BuildingDebugger.TestAllBuildings()
		elseif command == "/testremotes" then
			BuildingDebugger.TestRemoteEvents()
		end
	end
	
	player.Chatted:Connect(onChatted)
	
	print("🔧 Building Debugger initialized!")
	print("Commands: /debug, /testbuilding [type], /testall, /testremotes")
end

-- Auto-initialize
BuildingDebugger.Initialize()

return BuildingDebugger
