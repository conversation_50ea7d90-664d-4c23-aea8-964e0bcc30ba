-- BuildingDeletionUI.luau
-- Client-side building deletion system

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Wait for shared modules
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = Assets:WaitForChild("RemoteEvents")
local RemoteFunctions = Assets:WaitForChild("RemoteFunctions")

local BuildingDeletionUI = {}

-- Deletion state
local deletionMode = false
local deletionButton = nil
local selectedBuildings = {}
local selectionIndicators = {}

-- Create deletion button
function BuildingDeletionUI.CreateDeletionButton()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then
		warn("🗑️ UrbanSimUI not found for deletion button!")
		return
	end

	-- Create deletion button
	deletionButton = Instance.new("TextButton")
	deletionButton.Name = "DeletionButton"
	deletionButton.Size = UDim2.new(0, 120, 0, 40)
	deletionButton.Position = UDim2.new(1, -140, 0, 60) -- Below other buttons
	deletionButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
	deletionButton.Text = "🗑️ Delete Mode"
	deletionButton.TextColor3 = Color3.new(1, 1, 1)
	deletionButton.TextScaled = true
	deletionButton.Font = Enum.Font.SourceSansBold
	deletionButton.ZIndex = 5
	deletionButton.Visible = false -- Hidden by default
	deletionButton.Parent = screenGui

	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = deletionButton

	-- Button functionality
	deletionButton.MouseButton1Click:Connect(function()
		BuildingDeletionUI.ToggleDeletionMode()
	end)

	-- Hover effects
	deletionButton.MouseEnter:Connect(function()
		TweenService:Create(deletionButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(1, 0.4, 0.4)
		}):Play()
	end)

	deletionButton.MouseLeave:Connect(function()
		TweenService:Create(deletionButton, TweenInfo.new(0.2), {
			BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
		}):Play()
	end)

	print("🗑️ Building deletion button created!")
end

-- Toggle deletion mode
function BuildingDeletionUI.ToggleDeletionMode()
	deletionMode = not deletionMode
	
	if deletionMode then
		-- Enter deletion mode
		deletionButton.Text = "✅ Confirm Delete"
		deletionButton.BackgroundColor3 = Color3.new(0.3, 0.8, 0.3)
		
		-- Clear any existing building mode
		local ClientState = require(script.Parent:WaitForChild("ClientState"))
		if ClientState then
			ClientState.buildingMode = false
			ClientState.selectedBuildingType = nil
		end
		
		-- Show notification
		RemoteEvents.ShowNotification:FireClient(player, "Info", "🗑️ Deletion mode activated! Click buildings to select them for deletion.")
		
		print("🗑️ Deletion mode activated")
	else
		-- Exit deletion mode
		deletionButton.Text = "🗑️ Delete Mode"
		deletionButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
		
		-- Clear selections
		BuildingDeletionUI.ClearSelections()
		
		-- Show notification
		RemoteEvents.ShowNotification:FireClient(player, "Info", "🗑️ Deletion mode deactivated.")
		
		print("🗑️ Deletion mode deactivated")
	end
end

-- Show/hide deletion button
function BuildingDeletionUI.ShowDeletionButton()
	if deletionButton then
		deletionButton.Visible = true
	end
end

function BuildingDeletionUI.HideDeletionButton()
	if deletionButton then
		deletionButton.Visible = false
		-- Also exit deletion mode if active
		if deletionMode then
			BuildingDeletionUI.ToggleDeletionMode()
		end
	end
end

-- Handle building selection for deletion
function BuildingDeletionUI.HandleBuildingClick(buildingModel)
	if not deletionMode then return end
	
	local buildingId = buildingModel:GetAttribute("BuildingId")
	if not buildingId then
		warn("🗑️ Building has no BuildingId attribute!")
		return
	end
	
	-- Toggle selection
	if selectedBuildings[buildingId] then
		-- Deselect building
		selectedBuildings[buildingId] = nil
		BuildingDeletionUI.RemoveSelectionIndicator(buildingModel)
		print("🗑️ Deselected building:", buildingId)
	else
		-- Select building
		selectedBuildings[buildingId] = buildingModel
		BuildingDeletionUI.AddSelectionIndicator(buildingModel)
		print("🗑️ Selected building:", buildingId)
	end
	
	-- Update button text with count
	local count = 0
	for _ in pairs(selectedBuildings) do
		count = count + 1
	end
	
	if count > 0 then
		deletionButton.Text = "🗑️ Delete (" .. count .. ")"
	else
		deletionButton.Text = "✅ Confirm Delete"
	end
end

-- Add selection indicator to building
function BuildingDeletionUI.AddSelectionIndicator(buildingModel)
	-- Remove existing indicator
	BuildingDeletionUI.RemoveSelectionIndicator(buildingModel)
	
	-- Create selection indicator
	local indicator = Instance.new("SelectionBox")
	indicator.Name = "DeletionIndicator"
	indicator.Adornee = buildingModel.PrimaryPart or buildingModel:FindFirstChildOfClass("BasePart")
	indicator.Color3 = Color3.new(1, 0, 0) -- Red for deletion
	indicator.LineThickness = 0.3
	indicator.Transparency = 0.2
	indicator.Parent = buildingModel
	
	-- Store indicator reference
	selectionIndicators[buildingModel] = indicator
	
	-- Add pulsing animation
	local pulseConnection
	pulseConnection = RunService.Heartbeat:Connect(function()
		if indicator.Parent then
			local time = tick()
			indicator.Transparency = 0.2 + math.sin(time * 5) * 0.3
		else
			pulseConnection:Disconnect()
		end
	end)
end

-- Remove selection indicator from building
function BuildingDeletionUI.RemoveSelectionIndicator(buildingModel)
	local indicator = selectionIndicators[buildingModel]
	if indicator then
		indicator:Destroy()
		selectionIndicators[buildingModel] = nil
	end
end

-- Clear all selections
function BuildingDeletionUI.ClearSelections()
	-- Clear selection data
	selectedBuildings = {}
	
	-- Remove all indicators
	for buildingModel, indicator in pairs(selectionIndicators) do
		if indicator then
			indicator:Destroy()
		end
	end
	selectionIndicators = {}
	
	print("🗑️ Cleared all building selections")
end

-- Confirm deletion of selected buildings
function BuildingDeletionUI.ConfirmDeletion()
	if not deletionMode then return end
	
	local buildingIds = {}
	for buildingId, _ in pairs(selectedBuildings) do
		table.insert(buildingIds, buildingId)
	end
	
	if #buildingIds == 0 then
		RemoteEvents.ShowNotification:FireClient(player, "Warning", "🗑️ No buildings selected for deletion!")
		return
	end
	
	-- Create confirmation dialog
	BuildingDeletionUI.CreateConfirmationDialog(buildingIds)
end

-- Create confirmation dialog for deletion
function BuildingDeletionUI.CreateConfirmationDialog(buildingIds)
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end

	-- Create overlay
	local overlay = Instance.new("Frame")
	overlay.Name = "DeletionConfirmationOverlay"
	overlay.Size = UDim2.new(1, 0, 1, 0)
	overlay.Position = UDim2.new(0, 0, 0, 0)
	overlay.BackgroundColor3 = Color3.new(0, 0, 0)
	overlay.BackgroundTransparency = 0.5
	overlay.BorderSizePixel = 0
	overlay.ZIndex = 20
	overlay.Parent = screenGui

	-- Create dialog
	local dialog = Instance.new("Frame")
	dialog.Name = "DeletionConfirmationDialog"
	dialog.Size = UDim2.new(0, 400, 0, 250)
	dialog.Position = UDim2.new(0.5, -200, 0.5, -125)
	dialog.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	dialog.BorderSizePixel = 0
	dialog.ZIndex = 21
	dialog.Parent = overlay

	local dialogCorner = Instance.new("UICorner")
	dialogCorner.CornerRadius = UDim.new(0, 12)
	dialogCorner.Parent = dialog

	-- Title
	local titleLabel = Instance.new("TextLabel")
	titleLabel.Size = UDim2.new(1, -20, 0, 40)
	titleLabel.Position = UDim2.new(0, 10, 0, 10)
	titleLabel.BackgroundTransparency = 1
	titleLabel.Text = "🗑️ Confirm Building Deletion"
	titleLabel.TextColor3 = Color3.new(1, 1, 1)
	titleLabel.TextSize = 18
	titleLabel.Font = Enum.Font.SourceSansBold
	titleLabel.TextXAlignment = Enum.TextXAlignment.Left
	titleLabel.Parent = dialog

	-- Message
	local messageLabel = Instance.new("TextLabel")
	messageLabel.Size = UDim2.new(1, -20, 0, 100)
	messageLabel.Position = UDim2.new(0, 10, 0, 50)
	messageLabel.BackgroundTransparency = 1
	messageLabel.Text = "Are you sure you want to delete " .. #buildingIds .. " building(s)?\n\nThis action cannot be undone!\n\nYou will receive 50% of the building cost back."
	messageLabel.TextColor3 = Color3.new(0.9, 0.9, 0.9)
	messageLabel.TextSize = 14
	messageLabel.Font = Enum.Font.SourceSans
	messageLabel.TextXAlignment = Enum.TextXAlignment.Left
	messageLabel.TextYAlignment = Enum.TextYAlignment.Top
	messageLabel.TextWrapped = true
	messageLabel.Parent = dialog

	-- Confirm button
	local confirmButton = Instance.new("TextButton")
	confirmButton.Size = UDim2.new(0, 120, 0, 40)
	confirmButton.Position = UDim2.new(1, -250, 1, -60)
	confirmButton.BackgroundColor3 = Color3.new(0.8, 0.3, 0.3)
	confirmButton.Text = "🗑️ Delete Buildings"
	confirmButton.TextColor3 = Color3.new(1, 1, 1)
	confirmButton.TextSize = 14
	confirmButton.Font = Enum.Font.SourceSansBold
	confirmButton.Parent = dialog

	local confirmCorner = Instance.new("UICorner")
	confirmCorner.CornerRadius = UDim.new(0, 6)
	confirmCorner.Parent = confirmButton

	-- Cancel button
	local cancelButton = Instance.new("TextButton")
	cancelButton.Size = UDim2.new(0, 120, 0, 40)
	cancelButton.Position = UDim2.new(1, -120, 1, -60)
	cancelButton.BackgroundColor3 = Color3.new(0.4, 0.4, 0.4)
	cancelButton.Text = "✗ Cancel"
	cancelButton.TextColor3 = Color3.new(1, 1, 1)
	cancelButton.TextSize = 14
	cancelButton.Font = Enum.Font.SourceSansBold
	cancelButton.Parent = dialog

	local cancelCorner = Instance.new("UICorner")
	cancelCorner.CornerRadius = UDim.new(0, 6)
	cancelCorner.Parent = cancelButton

	-- Button functionality
	confirmButton.MouseButton1Click:Connect(function()
		overlay:Destroy()
		BuildingDeletionUI.ExecuteDeletion(buildingIds)
	end)

	cancelButton.MouseButton1Click:Connect(function()
		overlay:Destroy()
	end)

	-- Click overlay to cancel
	overlay.MouseButton1Click:Connect(function()
		overlay:Destroy()
	end)

	-- Animate dialog in
	dialog.Size = UDim2.new(0, 0, 0, 0)
	dialog.Position = UDim2.new(0.5, 0, 0.5, 0)

	TweenService:Create(dialog, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
		Size = UDim2.new(0, 400, 0, 250),
		Position = UDim2.new(0.5, -200, 0.5, -125)
	}):Play()
end

-- Execute building deletion
function BuildingDeletionUI.ExecuteDeletion(buildingIds)
	print("🗑️ Executing deletion of", #buildingIds, "buildings")
	
	-- Send deletion request to server
	local success, result = pcall(function()
		return RemoteFunctions.DeleteBuildings:InvokeServer(buildingIds)
	end)
	
	if success and result then
		-- Clear selections and exit deletion mode
		BuildingDeletionUI.ClearSelections()
		BuildingDeletionUI.ToggleDeletionMode()
		
		RemoteEvents.ShowNotification:FireClient(player, "Success", "🗑️ Buildings deleted successfully! Resources refunded.")
		print("🗑️ Buildings deleted successfully")
	else
		warn("🗑️ Failed to delete buildings:", result)
		RemoteEvents.ShowNotification:FireClient(player, "Error", "🗑️ Failed to delete buildings!")
	end
end

-- Initialize deletion system
function BuildingDeletionUI.Initialize()
	print("🗑️ Initializing Building Deletion UI...")
	
	-- Create deletion button
	BuildingDeletionUI.CreateDeletionButton()
	
	-- Handle input for deletion mode
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		-- Handle deletion mode with Delete key
		if input.KeyCode == Enum.KeyCode.Delete then
			if deletionMode then
				BuildingDeletionUI.ConfirmDeletion()
			else
				BuildingDeletionUI.ToggleDeletionMode()
			end
		end
		
		-- Handle building clicks in deletion mode
		if deletionMode and (input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch) then
			-- Raycast to find building
			local camera = workspace.CurrentCamera
			local mouse = Players.LocalPlayer:GetMouse()
			
			local ray = camera:ScreenPointToRay(mouse.X, mouse.Y)
			local raycastParams = RaycastParams.new()
			raycastParams.FilterType = Enum.RaycastFilterType.Blacklist
			raycastParams.FilterDescendantsInstances = {camera, player.Character}
			
			local raycastResult = workspace:Raycast(ray.Origin, ray.Direction * 1000, raycastParams)
			if raycastResult then
				local hitPart = raycastResult.Instance
				local buildingModel = hitPart.Parent
				
				-- Check if it's a building
				if buildingModel:GetAttribute("BuildingId") then
					BuildingDeletionUI.HandleBuildingClick(buildingModel)
				end
			end
		end
	end)
	
	print("✅ Building Deletion UI initialized!")
end

return BuildingDeletionUI
