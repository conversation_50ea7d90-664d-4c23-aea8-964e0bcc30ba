--[[
	Advanced Crafting UI System
	Visual crafting interface with drag-and-drop, progress bars, and queue management
	This module is loaded by the main client script
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Get shared modules
local Shared = ReplicatedStorage:WaitForChild("Shared")
local Config = require(Shared:WaitForChild("Config"))
local CraftingSystem = require(Shared:WaitForChild("CraftingSystem"))

-- Get RemoteEvents and RemoteFunctions (as instances, not modules)
local Assets = ReplicatedStorage:WaitForChild("Assets")

-- Initialize RemoteEvents and RemoteFunctions by requiring the modules first
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitFor<PERSON>hild("RemoteEvents"))
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitForChild("RemoteFunctions"))

-- Now access them as instances
local RemoteEvents = {
	CompleteCrafting = Assets:WaitForChild("CompleteCrafting"),
	CancelCrafting = Assets:WaitForChild("CancelCrafting"),
	StartCrafting = Assets:WaitForChild("StartCrafting"),
	CraftingStarted = Assets:WaitForChild("CraftingStarted"),
	CraftingCompleted = Assets:WaitForChild("CraftingCompleted"),
	CraftingCancelled = Assets:WaitForChild("CraftingCancelled"),
	ResourceUpdated = Assets:WaitForChild("ResourceUpdated")
}

local RemoteFunctions = {
	GetCraftingQueue = Assets:WaitForChild("GetCraftingQueue"),
	GetAvailableRecipes = Assets:WaitForChild("GetAvailableRecipes"),
	GetMaxCraftableQuantity = Assets:WaitForChild("GetMaxCraftableQuantity"),
	GetPlayerData = Assets:WaitForChild("GetPlayerData")
}

local CraftingUI = {}

-- UI State
local craftingWindow = nil
local craftingSlots = {}
local recipeButtons = {}
local playerData = {}

-- Create main crafting window
function CraftingUI.CreateCraftingWindow()
	if craftingWindow then
		craftingWindow:Destroy()
	end
	
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end
	
	-- Main crafting window
	craftingWindow = Instance.new("Frame")
	craftingWindow.Name = "CraftingWindow"
	craftingWindow.Size = UDim2.new(0, 800, 0, 600)
	craftingWindow.Position = UDim2.new(0.5, -400, 0.5, -300)
	craftingWindow.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	craftingWindow.BorderSizePixel = 0
	craftingWindow.Visible = false
	craftingWindow.Parent = screenGui
	
	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = craftingWindow
	
	-- Title bar
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 40)
	titleBar.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
	titleBar.BorderSizePixel = 0
	titleBar.Parent = craftingWindow
	
	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 12)
	titleCorner.Parent = titleBar
	
	-- Title text
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -100, 1, 0)
	titleText.Position = UDim2.new(0, 20, 0, 0)
	titleText.BackgroundTransparency = 1
	titleText.Text = "🏭  Crafting System"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextScaled = true
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextXAlignment = Enum.TextXAlignment.Left
	titleText.Parent = titleBar
	
	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -35, 0, 5)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = titleBar
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton
	
	-- Close button functionality
	closeButton.MouseButton1Click:Connect(function()
		CraftingUI.CloseCraftingWindow()
	end)
	
	-- Create crafting slots section
	CraftingUI.CreateCraftingSlots()
	
	-- Create recipe selection section
	CraftingUI.CreateRecipeSelection()
	
	-- Create resource display
	CraftingUI.CreateResourceDisplay()
	
	return craftingWindow
end

-- Create crafting slots with progress bars
function CraftingUI.CreateCraftingSlots()
	local slotsFrame = Instance.new("Frame")
	slotsFrame.Name = "CraftingSlots"
	slotsFrame.Size = UDim2.new(1, -20, 0, 150)
	slotsFrame.Position = UDim2.new(0, 10, 0, 50)
	slotsFrame.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
	slotsFrame.BorderSizePixel = 0
	slotsFrame.Parent = craftingWindow
	
	local slotsCorner = Instance.new("UICorner")
	slotsCorner.CornerRadius = UDim.new(0, 8)
	slotsCorner.Parent = slotsFrame
	
	-- Slots title
	local slotsTitle = Instance.new("TextLabel")
	slotsTitle.Name = "SlotsTitle"
	slotsTitle.Size = UDim2.new(1, 0, 0, 30)
	slotsTitle.BackgroundTransparency = 1
	slotsTitle.Text = "⚙️ Crafting Queue"
	slotsTitle.TextColor3 = Color3.new(1, 1, 1)
	slotsTitle.TextScaled = true
	slotsTitle.Font = Enum.Font.SourceSansBold
	slotsTitle.Parent = slotsFrame
	
	-- Create individual crafting slots
	craftingSlots = {}
	local maxSlots = 4 -- Will be dynamic based on player upgrades
	
	for i = 1, maxSlots do
		local slot = CraftingUI.CreateCraftingSlot(i)
		slot.Position = UDim2.new((i-1) * 0.25, 5, 0, 35)
		slot.Size = UDim2.new(0.25, -10, 1, -40)
		slot.Parent = slotsFrame
		craftingSlots[i] = slot
	end
end

-- Create individual crafting slot
function CraftingUI.CreateCraftingSlot(slotId)
	local slot = Instance.new("Frame")
	slot.Name = "CraftingSlot" .. slotId
	slot.BackgroundColor3 = Color3.new(0.15, 0.15, 0.15)
	slot.BorderSizePixel = 0
	
	local slotCorner = Instance.new("UICorner")
	slotCorner.CornerRadius = UDim.new(0, 6)
	slotCorner.Parent = slot
	
	-- Item icon/name
	local itemLabel = Instance.new("TextLabel")
	itemLabel.Name = "ItemLabel"
	itemLabel.Size = UDim2.new(1, -10, 0, 25)
	itemLabel.Position = UDim2.new(0, 5, 0, 5)
	itemLabel.BackgroundTransparency = 1
	itemLabel.Text = "Empty Slot"
	itemLabel.TextColor3 = Color3.new(0.7, 0.7, 0.7)
	itemLabel.TextScaled = true
	itemLabel.Font = Enum.Font.SourceSans
	itemLabel.Parent = slot
	
	-- Progress bar background
	local progressBg = Instance.new("Frame")
	progressBg.Name = "ProgressBackground"
	progressBg.Size = UDim2.new(1, -10, 0, 8)
	progressBg.Position = UDim2.new(0, 5, 0, 35)
	progressBg.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	progressBg.BorderSizePixel = 0
	progressBg.Parent = slot
	
	local progressBgCorner = Instance.new("UICorner")
	progressBgCorner.CornerRadius = UDim.new(0, 4)
	progressBgCorner.Parent = progressBg
	
	-- Progress bar fill
	local progressFill = Instance.new("Frame")
	progressFill.Name = "ProgressFill"
	progressFill.Size = UDim2.new(0, 0, 1, 0)
	progressFill.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
	progressFill.BorderSizePixel = 0
	progressFill.Parent = progressBg
	
	local progressFillCorner = Instance.new("UICorner")
	progressFillCorner.CornerRadius = UDim.new(0, 4)
	progressFillCorner.Parent = progressFill
	
	-- Time remaining label
	local timeLabel = Instance.new("TextLabel")
	timeLabel.Name = "TimeLabel"
	timeLabel.Size = UDim2.new(1, -10, 0, 20)
	timeLabel.Position = UDim2.new(0, 5, 0, 50)
	timeLabel.BackgroundTransparency = 1
	timeLabel.Text = ""
	timeLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	timeLabel.TextScaled = true
	timeLabel.Font = Enum.Font.SourceSans
	timeLabel.Parent = slot
	
	-- Action buttons frame
	local buttonsFrame = Instance.new("Frame")
	buttonsFrame.Name = "ButtonsFrame"
	buttonsFrame.Size = UDim2.new(1, -10, 0, 25)
	buttonsFrame.Position = UDim2.new(0, 5, 1, -30)
	buttonsFrame.BackgroundTransparency = 1
	buttonsFrame.Parent = slot
	
	-- Complete button
	local completeButton = Instance.new("TextButton")
	completeButton.Name = "CompleteButton"
	completeButton.Size = UDim2.new(0.45, 0, 1, 0)
	completeButton.Position = UDim2.new(0, 0, 0, 0)
	completeButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
	completeButton.Text = "✓"
	completeButton.TextColor3 = Color3.new(1, 1, 1)
	completeButton.TextScaled = true
	completeButton.Font = Enum.Font.SourceSansBold
	completeButton.Visible = false
	completeButton.Parent = buttonsFrame
	
	local completeCorner = Instance.new("UICorner")
	completeCorner.CornerRadius = UDim.new(0, 4)
	completeCorner.Parent = completeButton
	
	-- Cancel button
	local cancelButton = Instance.new("TextButton")
	cancelButton.Name = "CancelButton"
	cancelButton.Size = UDim2.new(0.45, 0, 1, 0)
	cancelButton.Position = UDim2.new(0.55, 0, 0, 0)
	cancelButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	cancelButton.Text = "×"
	cancelButton.TextColor3 = Color3.new(1, 1, 1)
	cancelButton.TextScaled = true
	cancelButton.Font = Enum.Font.SourceSansBold
	cancelButton.Visible = false
	cancelButton.Parent = buttonsFrame
	
	local cancelCorner = Instance.new("UICorner")
	cancelCorner.CornerRadius = UDim.new(0, 4)
	cancelCorner.Parent = cancelButton
	
	-- Button functionality
	completeButton.MouseButton1Click:Connect(function()
		RemoteEvents.CompleteCrafting:FireServer(slotId)
	end)
	
	cancelButton.MouseButton1Click:Connect(function()
		RemoteEvents.CancelCrafting:FireServer(slotId)
	end)
	
	return slot
end

-- Create recipe selection area
function CraftingUI.CreateRecipeSelection()
	local recipesFrame = Instance.new("ScrollingFrame")
	recipesFrame.Name = "RecipeSelection"
	recipesFrame.Size = UDim2.new(0.6, -15, 1, -220)
	recipesFrame.Position = UDim2.new(0, 10, 0, 210)
	recipesFrame.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
	recipesFrame.BorderSizePixel = 0
	recipesFrame.ScrollBarThickness = 8
	recipesFrame.Parent = craftingWindow
	
	local recipesCorner = Instance.new("UICorner")
	recipesCorner.CornerRadius = UDim.new(0, 8)
	recipesCorner.Parent = recipesFrame
	
	-- Recipes title
	local recipesTitle = Instance.new("TextLabel")
	recipesTitle.Name = "RecipesTitle"
	recipesTitle.Size = UDim2.new(1, 0, 0, 30)
	recipesTitle.BackgroundTransparency = 1
	recipesTitle.Text = "📋 Available Recipes"
	recipesTitle.TextColor3 = Color3.new(1, 1, 1)
	recipesTitle.TextScaled = true
	recipesTitle.Font = Enum.Font.SourceSansBold
	recipesTitle.Parent = recipesFrame
	
	-- Layout for recipe buttons
	local layout = Instance.new("UIListLayout")
	layout.SortOrder = Enum.SortOrder.LayoutOrder
	layout.Padding = UDim.new(0, 5)
	layout.Parent = recipesFrame
	
	return recipesFrame
end

-- Create resource display
function CraftingUI.CreateResourceDisplay()
	local resourceFrame = Instance.new("Frame")
	resourceFrame.Name = "ResourceDisplay"
	resourceFrame.Size = UDim2.new(0.4, -15, 1, -220)
	resourceFrame.Position = UDim2.new(0.6, 5, 0, 210)
	resourceFrame.BackgroundColor3 = Color3.new(0.05, 0.05, 0.05)
	resourceFrame.BorderSizePixel = 0
	resourceFrame.Parent = craftingWindow
	
	local resourceCorner = Instance.new("UICorner")
	resourceCorner.CornerRadius = UDim.new(0, 8)
	resourceCorner.Parent = resourceFrame
	
	-- Resources title
	local resourceTitle = Instance.new("TextLabel")
	resourceTitle.Name = "ResourceTitle"
	resourceTitle.Size = UDim2.new(1, 0, 0, 30)
	resourceTitle.BackgroundTransparency = 1
	resourceTitle.Text = "📦 Resources"
	resourceTitle.TextColor3 = Color3.new(1, 1, 1)
	resourceTitle.TextScaled = true
	resourceTitle.Font = Enum.Font.SourceSansBold
	resourceTitle.Parent = resourceFrame
	
	return resourceFrame
end

-- Show crafting window
function CraftingUI.ShowCraftingWindow()
	if not craftingWindow then
		CraftingUI.CreateCraftingWindow()
	end

	-- Play window open sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_OPEN")
		end
	end)

	craftingWindow.Visible = true

	-- Slide in animation
	craftingWindow.Position = UDim2.new(0.5, -400, 1, 0)
	craftingWindow:TweenPosition(
		UDim2.new(0.5, -400, 0.5, -300),
		Enum.EasingDirection.Out,
		Enum.EasingStyle.Back,
		0.5,
		true
	)

	-- Update content
	CraftingUI.UpdateCraftingWindow()
end

-- Close crafting window
function CraftingUI.CloseCraftingWindow()
	if craftingWindow then
		-- Play window close sound
		pcall(function()
			local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
			if SoundController and SoundController.PlayContextualSound then
				SoundController.PlayContextualSound("WINDOW_CLOSE")
			end
		end)

		craftingWindow:TweenPosition(
			UDim2.new(0.5, -400, 1, 0),
			Enum.EasingDirection.In,
			Enum.EasingStyle.Back,
			0.3,
			true,
			function()
				craftingWindow.Visible = false
			end
		)
	end
end

-- Update crafting window content
function CraftingUI.UpdateCraftingWindow()
	if not craftingWindow or not craftingWindow.Visible then return end
	
	-- Update crafting slots
	CraftingUI.UpdateCraftingSlots()
	
	-- Update available recipes
	CraftingUI.UpdateRecipeSelection()
	
	-- Update resource display
	CraftingUI.UpdateResourceDisplay()
end

-- Update crafting slots with current jobs
function CraftingUI.UpdateCraftingSlots()
	if not craftingWindow or not craftingWindow.Visible then return end

	-- Get current crafting queue from server
	local craftingQueue = RemoteFunctions.GetCraftingQueue:InvokeServer()
	if not craftingQueue then return end

	-- Update each crafting slot
	for i = 1, #craftingSlots do
		local slot = craftingSlots[i]
		local job = craftingQueue[i]

		if job and job.Status == "Active" then
			-- Update active job display
			CraftingUI.UpdateActiveSlot(slot, job)
		elseif job and job.Status == "Completed" then
			-- Update completed job display
			CraftingUI.UpdateCompletedSlot(slot, job)
		else
			-- Update empty slot display
			CraftingUI.UpdateEmptySlot(slot)
		end
	end
end

-- Update active crafting slot
function CraftingUI.UpdateActiveSlot(slot, job)
	local itemLabel = slot:FindFirstChild("ItemLabel")
	local progressFill = slot:FindFirstChild("ProgressBackground"):FindFirstChild("ProgressFill")
	local timeLabel = slot:FindFirstChild("TimeLabel")
	local completeButton = slot:FindFirstChild("ButtonsFrame"):FindFirstChild("CompleteButton")
	local cancelButton = slot:FindFirstChild("ButtonsFrame"):FindFirstChild("CancelButton")

	-- Update item name
	itemLabel.Text = job.Recipe:gsub("_", " ") .. " x" .. job.Quantity
	itemLabel.TextColor3 = Color3.new(1, 1, 1)

	-- Update progress bar
	local progress = math.min(job.Progress or 0, 1)
	progressFill:TweenSize(
		UDim2.new(progress, 0, 1, 0),
		Enum.EasingDirection.Out,
		Enum.EasingStyle.Quad,
		0.1,
		true
	)

	-- Update time remaining
	local timeRemaining = CraftingSystem.GetTimeRemaining(job)
	timeLabel.Text = CraftingSystem.FormatTime(timeRemaining)
	timeLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)

	-- Show/hide buttons
	completeButton.Visible = false
	cancelButton.Visible = true
end

-- Update completed crafting slot
function CraftingUI.UpdateCompletedSlot(slot, job)
	local itemLabel = slot:FindFirstChild("ItemLabel")
	local progressFill = slot:FindFirstChild("ProgressBackground"):FindFirstChild("ProgressFill")
	local timeLabel = slot:FindFirstChild("TimeLabel")
	local completeButton = slot:FindFirstChild("ButtonsFrame"):FindFirstChild("CompleteButton")
	local cancelButton = slot:FindFirstChild("ButtonsFrame"):FindFirstChild("CancelButton")

	-- Update item name
	itemLabel.Text = job.Recipe:gsub("_", " ") .. " x" .. job.Quantity .. " ✓"
	itemLabel.TextColor3 = Color3.new(0.2, 1, 0.2)

	-- Full progress bar
	progressFill.Size = UDim2.new(1, 0, 1, 0)
	progressFill.BackgroundColor3 = Color3.new(0.2, 1, 0.2)

	-- Completed text
	timeLabel.Text = "COMPLETED!"
	timeLabel.TextColor3 = Color3.new(0.2, 1, 0.2)

	-- Show complete button
	completeButton.Visible = true
	cancelButton.Visible = false
end

-- Update empty crafting slot
function CraftingUI.UpdateEmptySlot(slot)
	local itemLabel = slot:FindFirstChild("ItemLabel")
	local progressFill = slot:FindFirstChild("ProgressBackground"):FindFirstChild("ProgressFill")
	local timeLabel = slot:FindFirstChild("TimeLabel")
	local completeButton = slot:FindFirstChild("ButtonsFrame"):FindFirstChild("CompleteButton")
	local cancelButton = slot:FindFirstChild("ButtonsFrame"):FindFirstChild("CancelButton")

	-- Reset to empty state
	itemLabel.Text = "Empty Slot"
	itemLabel.TextColor3 = Color3.new(0.7, 0.7, 0.7)

	-- Empty progress bar
	progressFill.Size = UDim2.new(0, 0, 1, 0)
	progressFill.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)

	-- No time text
	timeLabel.Text = ""

	-- Hide buttons
	completeButton.Visible = false
	cancelButton.Visible = false
end

-- Update recipe selection
function CraftingUI.UpdateRecipeSelection()
	if not craftingWindow or not craftingWindow.Visible then return end

	local recipesFrame = craftingWindow:FindFirstChild("RecipeSelection")
	if not recipesFrame then return end

	-- Clear existing recipe buttons
	for _, child in ipairs(recipesFrame:GetChildren()) do
		if child.Name:find("RecipeButton") then
			child:Destroy()
		end
	end

	-- Get available recipes from server
	local availableRecipes = RemoteFunctions.GetAvailableRecipes:InvokeServer()
	if not availableRecipes then return end

	-- Create recipe buttons
	local yOffset = 40 -- Start after title
	for i, recipeData in ipairs(availableRecipes) do
		local recipeButton = CraftingUI.CreateRecipeButton(recipeData, i)
		recipeButton.Position = UDim2.new(0, 10, 0, yOffset)
		recipeButton.Parent = recipesFrame
		yOffset = yOffset + 80
	end

	-- Update scroll canvas size
	recipesFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset + 20)
end

-- Create recipe button
function CraftingUI.CreateRecipeButton(recipeData, index)
	local recipe = recipeData.Recipe
	local config = recipeData.Config
	local hasIngredients = recipeData.HasIngredients

	local button = Instance.new("Frame")
	button.Name = "RecipeButton" .. index
	button.Size = UDim2.new(1, -20, 0, 70)
	button.BackgroundColor3 = hasIngredients and Color3.new(0.15, 0.15, 0.15) or Color3.new(0.1, 0.1, 0.1)
	button.BorderSizePixel = 0

	local buttonCorner = Instance.new("UICorner")
	buttonCorner.CornerRadius = UDim.new(0, 6)
	buttonCorner.Parent = button

	-- Recipe name
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Name = "NameLabel"
	nameLabel.Size = UDim2.new(0.6, 0, 0, 25)
	nameLabel.Position = UDim2.new(0, 10, 0, 5)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = recipe:gsub("_", " ")
	nameLabel.TextColor3 = hasIngredients and Color3.new(1, 1, 1) or Color3.new(0.6, 0.6, 0.6)
	nameLabel.TextScaled = true
	nameLabel.Font = Enum.Font.SourceSansBold
	nameLabel.TextXAlignment = Enum.TextXAlignment.Left
	nameLabel.Parent = button

	-- Ingredients
	local ingredientsText = ""
	for ingredient, amount in pairs(config.Ingredients) do
		if ingredientsText ~= "" then ingredientsText = ingredientsText .. " + " end
		ingredientsText = ingredientsText .. ingredient .. " x" .. amount
	end

	local ingredientsLabel = Instance.new("TextLabel")
	ingredientsLabel.Name = "IngredientsLabel"
	ingredientsLabel.Size = UDim2.new(0.6, 0, 0, 20)
	ingredientsLabel.Position = UDim2.new(0, 10, 0, 30)
	ingredientsLabel.BackgroundTransparency = 1
	ingredientsLabel.Text = "Needs: " .. ingredientsText
	ingredientsLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	ingredientsLabel.TextScaled = true
	ingredientsLabel.Font = Enum.Font.SourceSans
	ingredientsLabel.TextXAlignment = Enum.TextXAlignment.Left
	ingredientsLabel.Parent = button

	-- Time and output
	local timeLabel = Instance.new("TextLabel")
	timeLabel.Name = "TimeLabel"
	timeLabel.Size = UDim2.new(0.6, 0, 0, 15)
	timeLabel.Position = UDim2.new(0, 10, 0, 50)
	timeLabel.BackgroundTransparency = 1
	timeLabel.Text = "Time: " .. CraftingSystem.FormatTime(config.Time) .. " → " .. recipe:gsub("_", " ") .. " x" .. config.Output
	timeLabel.TextColor3 = Color3.new(0.7, 0.7, 0.7)
	timeLabel.TextScaled = true
	timeLabel.Font = Enum.Font.SourceSans
	timeLabel.TextXAlignment = Enum.TextXAlignment.Left
	timeLabel.Parent = button

	-- Craft button
	local craftButton = Instance.new("TextButton")
	craftButton.Name = "CraftButton"
	craftButton.Size = UDim2.new(0.3, -10, 0, 50)
	craftButton.Position = UDim2.new(0.7, 0, 0, 10)
	craftButton.BackgroundColor3 = hasIngredients and Color3.new(0.2, 0.8, 0.2) or Color3.new(0.3, 0.3, 0.3)
	craftButton.Text = hasIngredients and "CRAFT" or "NO MATERIALS"
	craftButton.TextColor3 = Color3.new(1, 1, 1)
	craftButton.TextScaled = true
	craftButton.Font = Enum.Font.SourceSansBold
	craftButton.Active = hasIngredients
	craftButton.Parent = button

	local craftCorner = Instance.new("UICorner")
	craftCorner.CornerRadius = UDim.new(0, 4)
	craftCorner.Parent = craftButton

	-- Craft button functionality
	if hasIngredients then
		craftButton.MouseButton1Click:Connect(function()
			RemoteEvents.StartCrafting:FireServer(recipe, 1)
			-- Update UI after starting craft
			task.wait(0.1)
			CraftingUI.UpdateCraftingWindow()
		end)

		-- Hover effects
		craftButton.MouseEnter:Connect(function()
			craftButton.BackgroundColor3 = Color3.new(0.3, 1, 0.3)
		end)

		craftButton.MouseLeave:Connect(function()
			craftButton.BackgroundColor3 = Color3.new(0.2, 0.8, 0.2)
		end)
	end

	return button
end

-- Update resource display
function CraftingUI.UpdateResourceDisplay()
	if not craftingWindow or not craftingWindow.Visible then return end

	local resourceFrame = craftingWindow:FindFirstChild("ResourceDisplay")
	if not resourceFrame then return end

	-- Clear existing resource labels
	for _, child in ipairs(resourceFrame:GetChildren()) do
		if child.Name:find("ResourceLabel") then
			child:Destroy()
		end
	end

	-- Get current player data
	local playerData = RemoteFunctions.GetPlayerData:InvokeServer()
	if not playerData or not playerData.Resources then return end

	-- Create resource labels
	local yOffset = 40 -- Start after title
	local resourceList = {"Metal", "Plastic", "Wood", "CarteMere", "Metre", "PC"}

	for i, resource in ipairs(resourceList) do
		local amount = playerData.Resources[resource] or 0
		local resourceLabel = CraftingUI.CreateResourceLabel(resource, amount, i)
		resourceLabel.Position = UDim2.new(0, 10, 0, yOffset)
		resourceLabel.Parent = resourceFrame
		yOffset = yOffset + 35
	end
end

-- Create resource label
function CraftingUI.CreateResourceLabel(resource, amount, index)
	local label = Instance.new("Frame")
	label.Name = "ResourceLabel" .. index
	label.Size = UDim2.new(1, -20, 0, 30)
	label.BackgroundColor3 = Color3.new(0.1, 0.1, 0.1)
	label.BorderSizePixel = 0

	local labelCorner = Instance.new("UICorner")
	labelCorner.CornerRadius = UDim.new(0, 4)
	labelCorner.Parent = label

	-- Resource icon/name
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Name = "NameLabel"
	nameLabel.Size = UDim2.new(0.6, 0, 1, 0)
	nameLabel.Position = UDim2.new(0, 10, 0, 0)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = resource:gsub("_", " ")
	nameLabel.TextColor3 = Color3.new(1, 1, 1)
	nameLabel.TextScaled = true
	nameLabel.Font = Enum.Font.SourceSansBold
	nameLabel.TextXAlignment = Enum.TextXAlignment.Left
	nameLabel.Parent = label

	-- Resource amount
	local amountLabel = Instance.new("TextLabel")
	amountLabel.Name = "AmountLabel"
	amountLabel.Size = UDim2.new(0.4, -10, 1, 0)
	amountLabel.Position = UDim2.new(0.6, 0, 0, 0)
	amountLabel.BackgroundTransparency = 1
	amountLabel.Text = tostring(amount)
	amountLabel.TextColor3 = amount > 0 and Color3.new(0.2, 1, 0.2) or Color3.new(0.8, 0.8, 0.8)
	amountLabel.TextScaled = true
	amountLabel.Font = Enum.Font.SourceSansBold
	amountLabel.TextXAlignment = Enum.TextXAlignment.Right
	amountLabel.Parent = label

	return label
end

-- Real-time update system
local updateConnection = nil

-- Start real-time updates
function CraftingUI.StartUpdates()
	if updateConnection then
		updateConnection:Disconnect()
	end

	updateConnection = RunService.Heartbeat:Connect(function()
		if craftingWindow and craftingWindow.Visible then
			CraftingUI.UpdateCraftingSlots()
			-- Update resource display every 5 seconds
			if tick() % 5 < 0.1 then
				CraftingUI.UpdateResourceDisplay()
			end
		end
	end)
end

-- Stop real-time updates
function CraftingUI.StopUpdates()
	if updateConnection then
		updateConnection:Disconnect()
		updateConnection = nil
	end
end

-- Event handlers for crafting system
RemoteEvents.CraftingStarted.OnClientEvent:Connect(function(craftingJob)
	print("Crafting started:", craftingJob.Recipe, "in slot", craftingJob.SlotId)
	-- Update UI when crafting starts
	if craftingWindow and craftingWindow.Visible then
		CraftingUI.UpdateCraftingWindow()
	end
end)

RemoteEvents.CraftingCompleted.OnClientEvent:Connect(function(recipe, quantity)
	print("Crafting completed:", recipe, "x" .. quantity)
	-- Update UI when crafting completes
	if craftingWindow and craftingWindow.Visible then
		CraftingUI.UpdateCraftingWindow()
	end
end)

RemoteEvents.CraftingCancelled.OnClientEvent:Connect(function(slotId)
	print("Crafting cancelled in slot:", slotId)
	-- Update UI when crafting is cancelled
	if craftingWindow and craftingWindow.Visible then
		CraftingUI.UpdateCraftingWindow()
	end
end)

RemoteEvents.ResourceUpdated.OnClientEvent:Connect(function(resources)
	-- Update local player data cache
	if not playerData then playerData = {} end
	playerData.Resources = resources

	-- Update resource display if crafting window is open
	if craftingWindow and craftingWindow.Visible then
		CraftingUI.UpdateResourceDisplay()
		CraftingUI.UpdateRecipeSelection() -- Recipes may become available/unavailable
	end
end)

-- Initialize crafting UI system
function CraftingUI.Initialize()
	-- Start update system
	CraftingUI.StartUpdates()

	-- Set up cleanup on player leaving
	game.Players.PlayerRemoving:Connect(function(leavingPlayer)
		if leavingPlayer == player then
			CraftingUI.StopUpdates()
		end
	end)
end

-- Auto-initialize
CraftingUI.Initialize()

return CraftingUI
