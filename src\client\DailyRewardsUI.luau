--[[
	Daily Rewards UI System
	Beautiful animated daily rewards interface with TweenService
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Get shared modules
local Shared = ReplicatedStorage:WaitForChild("Shared")
local DailyRewards = require(Shared:WaitForChild("DailyRewards"))

-- Get RemoteEvents and RemoteFunctions (as instances)
local Assets = ReplicatedStorage:WaitForChild("Assets")

-- Initialize the modules first
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitForChild("RemoteEvents"))
require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("Assets"):WaitForChild("RemoteFunctions"))

-- Access as instances
local RemoteEvents = {
	ClaimDailyReward = Assets:WaitForChild("ClaimDailyReward"),
	ClaimMinuteReward = Assets:WaitForChild("ClaimMinuteReward")
}

local RemoteFunctions = {
	GetDailyStatus = Assets:WaitForChild("GetDailyStatus"),
	GetMinuteStatus = Assets:WaitForChild("GetMinuteStatus")
}

local DailyRewardsUI = {}

-- UI State
local rewardsWindow = nil
local dailyButton = nil
local minuteRewardButtons = {}
local isAnimating = false

-- Animation settings
local TWEEN_INFO = {
	SlideIn = TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
	SlideOut = TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In),
	Bounce = TweenInfo.new(0.3, Enum.EasingStyle.Elastic, Enum.EasingDirection.Out),
	Glow = TweenInfo.new(1, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, -1, true),
	Pulse = TweenInfo.new(0.8, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut, -1, true)
}

-- Create daily rewards button in main UI
function DailyRewardsUI.CreateDailyButton()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end
	
	-- Daily rewards button - positioned with proper spacing
	dailyButton = Instance.new("TextButton")
	dailyButton.Name = "DailyRewardsButton"
	dailyButton.Size = UDim2.new(0, 80, 0, 80)
	dailyButton.Position = UDim2.new(1, -100, 0, 70) -- Moved up and adjusted X for better spacing
	dailyButton.BackgroundColor3 = Color3.new(1, 0.8, 0.2)
	dailyButton.Text = "🎁"
	dailyButton.TextColor3 = Color3.new(1, 1, 1)
	dailyButton.TextScaled = true
	dailyButton.Font = Enum.Font.SourceSansBold
	dailyButton.Parent = screenGui
	
	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = dailyButton
	
	-- Add glow effect
	local glow = Instance.new("ImageLabel")
	glow.Name = "Glow"
	glow.Size = UDim2.new(1, 20, 1, 20)
	glow.Position = UDim2.new(0, -10, 0, -10)
	glow.BackgroundTransparency = 1
	glow.Image = "rbxasset://textures/ui/GuiImagePlaceholder.png"
	glow.ImageColor3 = Color3.new(1, 1, 0)
	glow.ImageTransparency = 0.7
	glow.ZIndex = dailyButton.ZIndex - 1
	glow.Parent = dailyButton
	
	local glowCorner = Instance.new("UICorner")
	glowCorner.CornerRadius = UDim.new(0, 16)
	glowCorner.Parent = glow
	
	-- Notification badge
	local badge = Instance.new("Frame")
	badge.Name = "NotificationBadge"
	badge.Size = UDim2.new(0, 20, 0, 20)
	badge.Position = UDim2.new(1, -10, 0, -10)
	badge.BackgroundColor3 = Color3.new(1, 0.2, 0.2)
	badge.Visible = false
	badge.Parent = dailyButton
	
	local badgeCorner = Instance.new("UICorner")
	badgeCorner.CornerRadius = UDim.new(0.5, 0)
	badgeCorner.Parent = badge
	
	local badgeText = Instance.new("TextLabel")
	badgeText.Size = UDim2.new(1, 0, 1, 0)
	badgeText.BackgroundTransparency = 1
	badgeText.Text = "!"
	badgeText.TextColor3 = Color3.new(1, 1, 1)
	badgeText.TextScaled = true
	badgeText.Font = Enum.Font.SourceSansBold
	badgeText.Parent = badge
	
	-- Button functionality
	dailyButton.MouseButton1Click:Connect(function()
		DailyRewardsUI.ShowRewardsWindow()
	end)
	
	-- Hover effects
	dailyButton.MouseEnter:Connect(function()
		if not isAnimating then
			TweenService:Create(dailyButton, TWEEN_INFO.Bounce, {Size = UDim2.new(0, 85, 0, 85)}):Play()
		end
	end)
	
	dailyButton.MouseLeave:Connect(function()
		if not isAnimating then
			TweenService:Create(dailyButton, TWEEN_INFO.Bounce, {Size = UDim2.new(0, 80, 0, 80)}):Play()
		end
	end)
	
	-- Start glow animation
	DailyRewardsUI.StartGlowAnimation()
	
	return dailyButton
end

-- Start glow animation
function DailyRewardsUI.StartGlowAnimation()
	if not dailyButton then return end
	
	local glow = dailyButton:FindFirstChild("Glow")
	if glow then
		local glowTween = TweenService:Create(glow, TWEEN_INFO.Glow, {ImageTransparency = 0.3})
		glowTween:Play()
	end
end

-- Show notification badge
function DailyRewardsUI.ShowNotificationBadge(show)
	if not dailyButton then return end
	
	local badge = dailyButton:FindFirstChild("NotificationBadge")
	if badge then
		badge.Visible = show
		if show then
			-- Pulse animation
			local pulseTween = TweenService:Create(badge, TWEEN_INFO.Pulse, {Size = UDim2.new(0, 25, 0, 25)})
			pulseTween:Play()
		end
	end
end

-- Create main rewards window
function DailyRewardsUI.CreateRewardsWindow()
	if rewardsWindow then
		rewardsWindow:Destroy()
	end
	
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end
	
	-- Main window
	rewardsWindow = Instance.new("Frame")
	rewardsWindow.Name = "DailyRewardsWindow"
	rewardsWindow.Size = UDim2.new(0, 900, 0, 600)
	rewardsWindow.Position = UDim2.new(0.5, -450, 0.5, -300)
	rewardsWindow.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
	rewardsWindow.BorderSizePixel = 0
	rewardsWindow.Visible = false
	rewardsWindow.Parent = screenGui
	
	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 16)
	corner.Parent = rewardsWindow
	
	-- Add gradient background
	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.1, 0.05, 0.2)),
		ColorSequenceKeypoint.new(1, Color3.new(0.05, 0.05, 0.1))
	}
	gradient.Rotation = 45
	gradient.Parent = rewardsWindow
	
	-- Title bar
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 60)
	titleBar.BackgroundColor3 = Color3.new(0.15, 0.1, 0.25)
	titleBar.BorderSizePixel = 0
	titleBar.Parent = rewardsWindow
	
	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 16)
	titleCorner.Parent = titleBar
	
	-- Title text
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -120, 1, 0)
	titleText.Position = UDim2.new(0, 20, 0, 0)
	titleText.BackgroundTransparency = 1
	titleText.Text = "🎁 Daily Rewards & Bonuses"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextScaled = true
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextXAlignment = Enum.TextXAlignment.Left
	titleText.Parent = titleBar
	
	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 40, 0, 40)
	closeButton.Position = UDim2.new(1, -50, 0, 10)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = titleBar
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 8)
	closeCorner.Parent = closeButton
	
	-- Close functionality
	closeButton.MouseButton1Click:Connect(function()
		DailyRewardsUI.CloseRewardsWindow()
	end)
	
	-- Create content sections
	DailyRewardsUI.CreateDailySection()
	DailyRewardsUI.CreateMinuteSection()
	DailyRewardsUI.CreateStreakSection()
	
	return rewardsWindow
end

-- Create daily reward section
function DailyRewardsUI.CreateDailySection()
	local dailySection = Instance.new("Frame")
	dailySection.Name = "DailySection"
	dailySection.Size = UDim2.new(0.6, -10, 1, -80)
	dailySection.Position = UDim2.new(0, 10, 0, 70)
	dailySection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	dailySection.BorderSizePixel = 0
	dailySection.Parent = rewardsWindow
	
	local sectionCorner = Instance.new("UICorner")
	sectionCorner.CornerRadius = UDim.new(0, 12)
	sectionCorner.Parent = dailySection
	
	-- Section title
	local sectionTitle = Instance.new("TextLabel")
	sectionTitle.Name = "SectionTitle"
	sectionTitle.Size = UDim2.new(1, -20, 0, 40)
	sectionTitle.Position = UDim2.new(0, 10, 0, 10)
	sectionTitle.BackgroundTransparency = 1
	sectionTitle.Text = "📅 Daily Login Reward"
	sectionTitle.TextColor3 = Color3.new(1, 1, 1)
	sectionTitle.TextScaled = true
	sectionTitle.Font = Enum.Font.SourceSansBold
	sectionTitle.TextXAlignment = Enum.TextXAlignment.Left
	sectionTitle.Parent = dailySection
	
	-- Daily reward card will be created dynamically
	return dailySection
end

-- Create minute rewards section
function DailyRewardsUI.CreateMinuteSection()
	local minuteSection = Instance.new("Frame")
	minuteSection.Name = "MinuteSection"
	minuteSection.Size = UDim2.new(0.4, -10, 0.5, -5)
	minuteSection.Position = UDim2.new(0.6, 0, 0, 70)
	minuteSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	minuteSection.BorderSizePixel = 0
	minuteSection.Parent = rewardsWindow
	
	local sectionCorner = Instance.new("UICorner")
	sectionCorner.CornerRadius = UDim.new(0, 12)
	sectionCorner.Parent = minuteSection
	
	-- Section title
	local sectionTitle = Instance.new("TextLabel")
	sectionTitle.Name = "SectionTitle"
	sectionTitle.Size = UDim2.new(1, -20, 0, 30)
	sectionTitle.Position = UDim2.new(0, 10, 0, 10)
	sectionTitle.BackgroundTransparency = 1
	sectionTitle.Text = "⏰ Minute Bonuses"
	sectionTitle.TextColor3 = Color3.new(1, 1, 1)
	sectionTitle.TextScaled = true
	sectionTitle.Font = Enum.Font.SourceSansBold
	sectionTitle.TextXAlignment = Enum.TextXAlignment.Left
	sectionTitle.Parent = minuteSection
	
	return minuteSection
end

-- Create streak section
function DailyRewardsUI.CreateStreakSection()
	local streakSection = Instance.new("Frame")
	streakSection.Name = "StreakSection"
	streakSection.Size = UDim2.new(0.4, -10, 0.5, -5)
	streakSection.Position = UDim2.new(0.6, 0, 0.5, 5)
	streakSection.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	streakSection.BorderSizePixel = 0
	streakSection.Parent = rewardsWindow
	
	local sectionCorner = Instance.new("UICorner")
	sectionCorner.CornerRadius = UDim.new(0, 12)
	sectionCorner.Parent = streakSection
	
	-- Section title
	local sectionTitle = Instance.new("TextLabel")
	sectionTitle.Name = "SectionTitle"
	sectionTitle.Size = UDim2.new(1, -20, 0, 30)
	sectionTitle.Position = UDim2.new(0, 10, 0, 10)
	sectionTitle.BackgroundTransparency = 1
	sectionTitle.Text = "🔥 Login Streak"
	sectionTitle.TextColor3 = Color3.new(1, 1, 1)
	sectionTitle.TextScaled = true
	sectionTitle.Font = Enum.Font.SourceSansBold
	sectionTitle.TextXAlignment = Enum.TextXAlignment.Left
	sectionTitle.Parent = streakSection
	
	return streakSection
end

-- Show rewards window with animation
function DailyRewardsUI.ShowRewardsWindow()
	print("🎁 ShowRewardsWindow called")

	if isAnimating then
		print("🎁 Already animating, returning")
		return
	end
	isAnimating = true

	if not rewardsWindow then
		print("🎁 Creating rewards window...")
		DailyRewardsUI.CreateRewardsWindow()
	end

	if not rewardsWindow then
		print("🎁 ERROR: Failed to create rewards window!")
		isAnimating = false
		return
	end

	print("🎁 Updating rewards window content...")
	-- Update content
	DailyRewardsUI.UpdateRewardsWindow()

	-- Play window open sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_OPEN")
		end
	end)

	print("🎁 Making window visible and animating...")
	rewardsWindow.Visible = true

	-- Slide in animation
	rewardsWindow.Position = UDim2.new(0.5, -450, 1, 0)
	local slideInTween = TweenService:Create(rewardsWindow, TWEEN_INFO.SlideIn, {
		Position = UDim2.new(0.5, -450, 0.5, -300)
	})

	slideInTween.Completed:Connect(function()
		print("🎁 Animation completed")
		isAnimating = false
	end)

	slideInTween:Play()
	print("🎁 Animation started")
end

-- Close rewards window with animation
function DailyRewardsUI.CloseRewardsWindow()
	if not rewardsWindow or isAnimating then return end
	isAnimating = true

	-- Play window close sound
	pcall(function()
		local SoundController = require(game.Players.LocalPlayer.PlayerScripts.SoundController)
		if SoundController and SoundController.PlayContextualSound then
			SoundController.PlayContextualSound("WINDOW_CLOSE")
		end
	end)

	local slideOutTween = TweenService:Create(rewardsWindow, TWEEN_INFO.SlideOut, {
		Position = UDim2.new(0.5, -450, 1, 0)
	})
	
	slideOutTween.Completed:Connect(function()
		rewardsWindow.Visible = false
		isAnimating = false
	end)
	
	slideOutTween:Play()
end

-- Update rewards window content
function DailyRewardsUI.UpdateRewardsWindow()
	if not rewardsWindow then return end

	-- Get current status from server with error handling
	local dailyStatus, minuteStatus

	print("🎁 Getting daily status from server...")
	local success1, result1 = pcall(function()
		return RemoteFunctions.GetDailyStatus:InvokeServer()
	end)

	if success1 and result1 then
		dailyStatus = result1
		print("🎁 Got daily status from server:", dailyStatus.Day, dailyStatus.CanClaim)
	else
		warn("🎁 Failed to get daily status:", result1)
		-- Use enhanced default data
		dailyStatus = {
			Day = 1,
			Streak = 1,
			CanClaim = true,
			Tier = "Basic",
			NextReward = {
				Pieces = 500,
				XP = 100,
				Cash = 50
			}
		}
		print("🎁 Using fallback daily status")
	end

	print("🎁 Getting minute status from server...")
	local success2, result2 = pcall(function()
		return RemoteFunctions.GetMinuteStatus:InvokeServer()
	end)

	if success2 and result2 then
		minuteStatus = result2
		print("🎁 Got minute status from server")
	else
		warn("🎁 Failed to get minute status:", result2)
		-- Use default data
		minuteStatus = {
			NextReward = 5,
			TimeLeft = 300
		}
		print("🎁 Using fallback minute status")
	end

	-- Update sections with data
	print("🎁 Updating daily section...")
	DailyRewardsUI.UpdateDailySection(dailyStatus)
	print("🎁 Updating streak section...")
	DailyRewardsUI.UpdateStreakSection(dailyStatus)
	print("🎁 Updating minute section...")
	DailyRewardsUI.UpdateMinuteSection(minuteStatus)
	print("🎁 All sections updated!")
end

-- Update daily section
function DailyRewardsUI.UpdateDailySection(status)
	local dailySection = rewardsWindow:FindFirstChild("DailySection")
	if not dailySection then return end

	-- Clear existing content
	for _, child in ipairs(dailySection:GetChildren()) do
		if child.Name ~= "SectionTitle" and not child:IsA("UICorner") then
			child:Destroy()
		end
	end

	-- Create reward card
	local rewardCard = Instance.new("Frame")
	rewardCard.Name = "RewardCard"
	rewardCard.Size = UDim2.new(1, -20, 0, 150)
	rewardCard.Position = UDim2.new(0, 10, 0, 60)
	rewardCard.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	rewardCard.BorderSizePixel = 0
	rewardCard.Parent = dailySection

	local cardCorner = Instance.new("UICorner")
	cardCorner.CornerRadius = UDim.new(0, 8)
	cardCorner.Parent = rewardCard

	-- Day info
	local dayLabel = Instance.new("TextLabel")
	dayLabel.Size = UDim2.new(1, -20, 0, 30)
	dayLabel.Position = UDim2.new(0, 10, 0, 10)
	dayLabel.BackgroundTransparency = 1
	dayLabel.Text = "Day " .. (status.Day or 1) .. " Reward"
	dayLabel.TextColor3 = Color3.new(1, 1, 1)
	dayLabel.TextScaled = true
	dayLabel.Font = Enum.Font.SourceSansBold
	dayLabel.TextXAlignment = Enum.TextXAlignment.Left
	dayLabel.Parent = rewardCard

	-- Reward info
	local rewardInfo = Instance.new("TextLabel")
	rewardInfo.Size = UDim2.new(1, -20, 0, 60)
	rewardInfo.Position = UDim2.new(0, 10, 0, 50)
	rewardInfo.BackgroundTransparency = 1
	local reward = status.NextReward or {Pieces = 500, XP = 100}
	rewardInfo.Text = "💰 " .. (reward.Pieces or 500) .. " Pieces\n⭐ " .. (reward.XP or 100) .. " XP"
	rewardInfo.TextColor3 = Color3.new(0.8, 0.8, 0.8)
	rewardInfo.TextSize = 16
	rewardInfo.Font = Enum.Font.SourceSans
	rewardInfo.TextXAlignment = Enum.TextXAlignment.Left
	rewardInfo.Parent = rewardCard

	-- Claim button
	local claimButton = Instance.new("TextButton")
	claimButton.Size = UDim2.new(1, -20, 0, 35)
	claimButton.Position = UDim2.new(0, 10, 1, -45)
	claimButton.BackgroundColor3 = status.CanClaim and Color3.new(0.2, 0.8, 0.2) or Color3.new(0.3, 0.3, 0.3)
	claimButton.Text = status.CanClaim and "🎁 Claim Reward" or "⏰ Already Claimed"
	claimButton.TextColor3 = Color3.new(1, 1, 1)
	claimButton.TextScaled = true
	claimButton.Font = Enum.Font.SourceSansBold
	claimButton.Parent = rewardCard

	local claimCorner = Instance.new("UICorner")
	claimCorner.CornerRadius = UDim.new(0, 6)
	claimCorner.Parent = claimButton

	if status.CanClaim then
		claimButton.MouseButton1Click:Connect(function()
			RemoteEvents.ClaimDailyReward:FireServer()
		end)
	end
end

-- Update minute section
function DailyRewardsUI.UpdateMinuteSection(status)
	local minuteSection = rewardsWindow:FindFirstChild("MinuteSection")
	if not minuteSection then return end

	-- Clear existing content
	for _, child in ipairs(minuteSection:GetChildren()) do
		if child.Name ~= "SectionTitle" and not child:IsA("UICorner") then
			child:Destroy()
		end
	end

	-- Create minute reward info
	local minuteInfo = Instance.new("TextLabel")
	minuteInfo.Size = UDim2.new(1, -20, 0, 80)
	minuteInfo.Position = UDim2.new(0, 10, 0, 50)
	minuteInfo.BackgroundTransparency = 1
	minuteInfo.Text = "Next reward in:\n" .. math.floor((status.TimeLeft or 300) / 60) .. " minutes"
	minuteInfo.TextColor3 = Color3.new(1, 1, 1)
	minuteInfo.TextSize = 14
	minuteInfo.Font = Enum.Font.SourceSans
	minuteInfo.TextXAlignment = Enum.TextXAlignment.Center
	minuteInfo.Parent = minuteSection
end

-- Update streak section
function DailyRewardsUI.UpdateStreakSection(status)
	local streakSection = rewardsWindow:FindFirstChild("StreakSection")
	if not streakSection then return end

	-- Clear existing content
	for _, child in ipairs(streakSection:GetChildren()) do
		if child.Name ~= "SectionTitle" and not child:IsA("UICorner") then
			child:Destroy()
		end
	end

	-- Create streak info
	local streakInfo = Instance.new("TextLabel")
	streakInfo.Size = UDim2.new(1, -20, 0, 80)
	streakInfo.Position = UDim2.new(0, 10, 0, 50)
	streakInfo.BackgroundTransparency = 1
	streakInfo.Text = "🔥 Current Streak:\n" .. (status.Streak or 1) .. " days"
	streakInfo.TextColor3 = Color3.new(1, 1, 1)
	streakInfo.TextSize = 14
	streakInfo.Font = Enum.Font.SourceSans
	streakInfo.TextXAlignment = Enum.TextXAlignment.Center
	streakInfo.Parent = streakSection
end

-- Initialize daily rewards UI
function DailyRewardsUI.Initialize()
	DailyRewardsUI.CreateDailyButton()
	
	-- Event handlers
	RemoteEvents.DailyRewardAvailable.OnClientEvent:Connect(function(day)
		DailyRewardsUI.ShowNotificationBadge(true)
	end)
	
	RemoteEvents.DailyRewardClaimed.OnClientEvent:Connect(function(rewardData)
		DailyRewardsUI.ShowNotificationBadge(false)
		-- Show reward animation
	end)
	
	RemoteEvents.MinuteRewardAvailable.OnClientEvent:Connect(function(rewardData)
		-- Show minute reward notification
	end)
end

-- Note: Initialize() will be called from main client after UI is created

return DailyRewardsUI
