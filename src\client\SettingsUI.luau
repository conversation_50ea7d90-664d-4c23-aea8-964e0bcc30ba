--[[
	Settings UI System
	Advanced settings menu with animations and mobile optimization
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

local SettingsUI = {}

-- Settings data
local settings = {
	Graphics = {
		Quality = "High",
		Shadows = true,
		Particles = true,
		UIAnimations = true
	},
	Audio = {
		MasterVolume = 0.8,
		MusicVolume = 0.6,
		SFXVolume = 0.8,
		UIVolume = 0.5
	},
	Gameplay = {
		AutoSave = true,
		Notifications = true,
		Tutorials = true,
		GridSnap = true
	},
	Controls = {
		CameraSpeed = 0.5,
		ZoomSpeed = 0.5,
		InvertY = false,
		TouchControls = true
	}
}

-- UI Elements
local settingsWindow = nil
local isOpen = false

-- Create settings window
function SettingsUI.CreateSettingsWindow()
	if settingsWindow then
		settingsWindow:Destroy()
	end
	
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end
	
	-- Main settings window
	settingsWindow = Instance.new("Frame")
	settingsWindow.Name = "SettingsWindow"
	settingsWindow.Size = UDim2.new(0, 800, 0, 600)
	settingsWindow.Position = UDim2.new(0.5, -400, 0.5, -300)
	settingsWindow.BackgroundColor3 = Color3.new(0.05, 0.05, 0.1)
	settingsWindow.BorderSizePixel = 0
	settingsWindow.Visible = false
	settingsWindow.Parent = screenGui
	
	-- Add corner radius and gradient
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 16)
	corner.Parent = settingsWindow
	
	local gradient = Instance.new("UIGradient")
	gradient.Color = ColorSequence.new{
		ColorSequenceKeypoint.new(0, Color3.new(0.1, 0.05, 0.15)),
		ColorSequenceKeypoint.new(1, Color3.new(0.05, 0.05, 0.1))
	}
	gradient.Rotation = 45
	gradient.Parent = settingsWindow
	
	-- Title bar
	local titleBar = Instance.new("Frame")
	titleBar.Name = "TitleBar"
	titleBar.Size = UDim2.new(1, 0, 0, 60)
	titleBar.BackgroundColor3 = Color3.new(0.15, 0.1, 0.2)
	titleBar.BorderSizePixel = 0
	titleBar.Parent = settingsWindow
	
	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 16)
	titleCorner.Parent = titleBar
	
	-- Title text
	local titleText = Instance.new("TextLabel")
	titleText.Name = "TitleText"
	titleText.Size = UDim2.new(1, -120, 1, 0)
	titleText.Position = UDim2.new(0, 20, 0, 0)
	titleText.BackgroundTransparency = 1
	titleText.Text = "⚙️ Game Settings"
	titleText.TextColor3 = Color3.new(1, 1, 1)
	titleText.TextScaled = true
	titleText.Font = Enum.Font.SourceSansBold
	titleText.TextXAlignment = Enum.TextXAlignment.Left
	titleText.Parent = titleBar
	
	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 40, 0, 40)
	closeButton.Position = UDim2.new(1, -50, 0, 10)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = titleBar
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 8)
	closeCorner.Parent = closeButton
	
	-- Close functionality
	closeButton.MouseButton1Click:Connect(function()
		SettingsUI.CloseSettings()
	end)
	
	-- Create tabs
	SettingsUI.CreateTabs()
	
	-- Create content area
	SettingsUI.CreateContentArea()
	
	return settingsWindow
end

-- Create tab system
function SettingsUI.CreateTabs()
	local tabContainer = Instance.new("Frame")
	tabContainer.Name = "TabContainer"
	tabContainer.Size = UDim2.new(0, 150, 1, -80)
	tabContainer.Position = UDim2.new(0, 10, 0, 70)
	tabContainer.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	tabContainer.BorderSizePixel = 0
	tabContainer.Parent = settingsWindow
	
	local tabCorner = Instance.new("UICorner")
	tabCorner.CornerRadius = UDim.new(0, 12)
	tabCorner.Parent = tabContainer
	
	-- Tab buttons
	local tabs = {"Graphics", "Audio", "Gameplay", "Controls"}
	local tabIcons = {"🎨", "🔊", "🎮", "🎯"}
	
	for i, tabName in ipairs(tabs) do
		local tabButton = Instance.new("TextButton")
		tabButton.Name = tabName .. "Tab"
		tabButton.Size = UDim2.new(1, -10, 0, 50)
		tabButton.Position = UDim2.new(0, 5, 0, 10 + (i-1) * 55)
		tabButton.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
		tabButton.Text = tabIcons[i] .. " " .. tabName
		tabButton.TextColor3 = Color3.new(1, 1, 1)
		tabButton.TextScaled = true
		tabButton.Font = Enum.Font.SourceSansBold
		tabButton.Parent = tabContainer
		
		local buttonCorner = Instance.new("UICorner")
		buttonCorner.CornerRadius = UDim.new(0, 8)
		buttonCorner.Parent = tabButton
		
		-- Tab functionality
		tabButton.MouseButton1Click:Connect(function()
			SettingsUI.SwitchTab(tabName)
		end)
		
		-- Hover effects
		tabButton.MouseEnter:Connect(function()
			TweenService:Create(tabButton, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
			}):Play()
		end)
		
		tabButton.MouseLeave:Connect(function()
			TweenService:Create(tabButton, TweenInfo.new(0.2), {
				BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
			}):Play()
		end)
	end
end

-- Create content area
function SettingsUI.CreateContentArea()
	local contentArea = Instance.new("ScrollingFrame")
	contentArea.Name = "ContentArea"
	contentArea.Size = UDim2.new(1, -180, 1, -80)
	contentArea.Position = UDim2.new(0, 170, 0, 70)
	contentArea.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	contentArea.BorderSizePixel = 0
	contentArea.ScrollBarThickness = 8
	contentArea.Parent = settingsWindow
	
	local contentCorner = Instance.new("UICorner")
	contentCorner.CornerRadius = UDim.new(0, 12)
	contentCorner.Parent = contentArea
	
	-- Layout
	local layout = Instance.new("UIListLayout")
	layout.SortOrder = Enum.SortOrder.LayoutOrder
	layout.Padding = UDim.new(0, 10)
	layout.Parent = contentArea
	
	return contentArea
end

-- Switch tab
function SettingsUI.SwitchTab(tabName)
	local contentArea = settingsWindow:FindFirstChild("ContentArea")
	if not contentArea then return end
	
	-- Clear existing content
	for _, child in ipairs(contentArea:GetChildren()) do
		if child:IsA("Frame") then
			child:Destroy()
		end
	end
	
	-- Create content based on tab
	if tabName == "Graphics" then
		SettingsUI.CreateGraphicsSettings(contentArea)
	elseif tabName == "Audio" then
		SettingsUI.CreateAudioSettings(contentArea)
	elseif tabName == "Gameplay" then
		SettingsUI.CreateGameplaySettings(contentArea)
	elseif tabName == "Controls" then
		SettingsUI.CreateControlsSettings(contentArea)
	end
	
	-- Update canvas size
	contentArea.CanvasSize = UDim2.new(0, 0, 0, contentArea.UIListLayout.AbsoluteContentSize.Y + 20)
end

-- Create graphics settings
function SettingsUI.CreateGraphicsSettings(parent)
	-- Quality dropdown
	SettingsUI.CreateDropdown(parent, "Graphics Quality", {"Low", "Medium", "High", "Ultra"}, settings.Graphics.Quality)
	
	-- Toggle switches
	SettingsUI.CreateToggle(parent, "Shadows", settings.Graphics.Shadows)
	SettingsUI.CreateToggle(parent, "Particles", settings.Graphics.Particles)
	SettingsUI.CreateToggle(parent, "UI Animations", settings.Graphics.UIAnimations)
end

-- Create audio settings
function SettingsUI.CreateAudioSettings(parent)
	-- Volume sliders
	SettingsUI.CreateSlider(parent, "Master Volume", settings.Audio.MasterVolume)
	SettingsUI.CreateSlider(parent, "Music Volume", settings.Audio.MusicVolume)
	SettingsUI.CreateSlider(parent, "SFX Volume", settings.Audio.SFXVolume)
	SettingsUI.CreateSlider(parent, "UI Volume", settings.Audio.UIVolume)
end

-- Create gameplay settings
function SettingsUI.CreateGameplaySettings(parent)
	SettingsUI.CreateToggle(parent, "Auto Save", settings.Gameplay.AutoSave)
	SettingsUI.CreateToggle(parent, "Notifications", settings.Gameplay.Notifications)
	SettingsUI.CreateToggle(parent, "Tutorials", settings.Gameplay.Tutorials)
	SettingsUI.CreateToggle(parent, "Grid Snap", settings.Gameplay.GridSnap)
end

-- Create controls settings
function SettingsUI.CreateControlsSettings(parent)
	SettingsUI.CreateSlider(parent, "Camera Speed", settings.Controls.CameraSpeed)
	SettingsUI.CreateSlider(parent, "Zoom Speed", settings.Controls.ZoomSpeed)
	SettingsUI.CreateToggle(parent, "Invert Y Axis", settings.Controls.InvertY)
	SettingsUI.CreateToggle(parent, "Touch Controls", settings.Controls.TouchControls)
end

-- Create toggle switch
function SettingsUI.CreateToggle(parent, name, value)
	local toggleFrame = Instance.new("Frame")
	toggleFrame.Name = name .. "Toggle"
	toggleFrame.Size = UDim2.new(1, -20, 0, 50)
	toggleFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	toggleFrame.BorderSizePixel = 0
	toggleFrame.Parent = parent
	
	local toggleCorner = Instance.new("UICorner")
	toggleCorner.CornerRadius = UDim.new(0, 8)
	toggleCorner.Parent = toggleFrame
	
	-- Label
	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(0.7, 0, 1, 0)
	label.Position = UDim2.new(0, 15, 0, 0)
	label.BackgroundTransparency = 1
	label.Text = name
	label.TextColor3 = Color3.new(1, 1, 1)
	label.TextScaled = true
	label.Font = Enum.Font.SourceSans
	label.TextXAlignment = Enum.TextXAlignment.Left
	label.Parent = toggleFrame
	
	-- Toggle switch
	local switch = Instance.new("TextButton")
	switch.Size = UDim2.new(0, 60, 0, 30)
	switch.Position = UDim2.new(1, -75, 0.5, -15)
	switch.BackgroundColor3 = value and Color3.new(0.2, 0.8, 0.2) or Color3.new(0.3, 0.3, 0.3)
	switch.Text = value and "ON" or "OFF"
	switch.TextColor3 = Color3.new(1, 1, 1)
	switch.TextScaled = true
	switch.Font = Enum.Font.SourceSansBold
	switch.Parent = toggleFrame
	
	local switchCorner = Instance.new("UICorner")
	switchCorner.CornerRadius = UDim.new(0, 15)
	switchCorner.Parent = switch
	
	-- Toggle functionality
	switch.MouseButton1Click:Connect(function()
		value = not value
		switch.Text = value and "ON" or "OFF"
		TweenService:Create(switch, TweenInfo.new(0.2), {
			BackgroundColor3 = value and Color3.new(0.2, 0.8, 0.2) or Color3.new(0.3, 0.3, 0.3)
		}):Play()
	end)
	
	return toggleFrame
end

-- Create slider
function SettingsUI.CreateSlider(parent, name, value)
	local sliderFrame = Instance.new("Frame")
	sliderFrame.Name = name .. "Slider"
	sliderFrame.Size = UDim2.new(1, -20, 0, 60)
	sliderFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	sliderFrame.BorderSizePixel = 0
	sliderFrame.Parent = parent
	
	local sliderCorner = Instance.new("UICorner")
	sliderCorner.CornerRadius = UDim.new(0, 8)
	sliderCorner.Parent = sliderFrame
	
	-- Label
	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(1, -20, 0, 25)
	label.Position = UDim2.new(0, 10, 0, 5)
	label.BackgroundTransparency = 1
	label.Text = name .. ": " .. math.floor(value * 100) .. "%"
	label.TextColor3 = Color3.new(1, 1, 1)
	label.TextScaled = true
	label.Font = Enum.Font.SourceSans
	label.TextXAlignment = Enum.TextXAlignment.Left
	label.Parent = sliderFrame
	
	-- Slider track
	local track = Instance.new("Frame")
	track.Size = UDim2.new(1, -40, 0, 8)
	track.Position = UDim2.new(0, 20, 1, -20)
	track.BackgroundColor3 = Color3.new(0.2, 0.2, 0.2)
	track.BorderSizePixel = 0
	track.Parent = sliderFrame
	
	local trackCorner = Instance.new("UICorner")
	trackCorner.CornerRadius = UDim.new(0, 4)
	trackCorner.Parent = track
	
	-- Slider fill
	local fill = Instance.new("Frame")
	fill.Size = UDim2.new(value, 0, 1, 0)
	fill.BackgroundColor3 = Color3.new(0.2, 0.6, 1)
	fill.BorderSizePixel = 0
	fill.Parent = track
	
	local fillCorner = Instance.new("UICorner")
	fillCorner.CornerRadius = UDim.new(0, 4)
	fillCorner.Parent = fill
	
	return sliderFrame
end

-- Create dropdown
function SettingsUI.CreateDropdown(parent, name, options, selected)
	local dropdownFrame = Instance.new("Frame")
	dropdownFrame.Name = name .. "Dropdown"
	dropdownFrame.Size = UDim2.new(1, -20, 0, 50)
	dropdownFrame.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	dropdownFrame.BorderSizePixel = 0
	dropdownFrame.Parent = parent
	
	local dropdownCorner = Instance.new("UICorner")
	dropdownCorner.CornerRadius = UDim.new(0, 8)
	dropdownCorner.Parent = dropdownFrame
	
	-- Label
	local label = Instance.new("TextLabel")
	label.Size = UDim2.new(0.5, 0, 1, 0)
	label.Position = UDim2.new(0, 15, 0, 0)
	label.BackgroundTransparency = 1
	label.Text = name
	label.TextColor3 = Color3.new(1, 1, 1)
	label.TextScaled = true
	label.Font = Enum.Font.SourceSans
	label.TextXAlignment = Enum.TextXAlignment.Left
	label.Parent = dropdownFrame
	
	-- Dropdown button
	local dropdown = Instance.new("TextButton")
	dropdown.Size = UDim2.new(0.4, 0, 0, 30)
	dropdown.Position = UDim2.new(0.55, 0, 0.5, -15)
	dropdown.BackgroundColor3 = Color3.new(0.2, 0.2, 0.25)
	dropdown.Text = selected .. " ▼"
	dropdown.TextColor3 = Color3.new(1, 1, 1)
	dropdown.TextScaled = true
	dropdown.Font = Enum.Font.SourceSans
	dropdown.Parent = dropdownFrame
	
	local dropdownButtonCorner = Instance.new("UICorner")
	dropdownButtonCorner.CornerRadius = UDim.new(0, 6)
	dropdownButtonCorner.Parent = dropdown
	
	return dropdownFrame
end

-- Show settings window
function SettingsUI.ShowSettings()
	if isOpen then return end
	isOpen = true
	
	if not settingsWindow then
		SettingsUI.CreateSettingsWindow()
	end
	
	settingsWindow.Visible = true
	
	-- Slide in animation
	settingsWindow.Position = UDim2.new(0.5, -400, 1, 0)
	TweenService:Create(settingsWindow, TweenInfo.new(0.5, Enum.EasingStyle.Back, Enum.EasingDirection.Out), {
		Position = UDim2.new(0.5, -400, 0.5, -300)
	}):Play()
	
	-- Show graphics tab by default
	SettingsUI.SwitchTab("Graphics")
end

-- Close settings window
function SettingsUI.CloseSettings()
	if not isOpen or not settingsWindow then return end
	isOpen = false
	
	TweenService:Create(settingsWindow, TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.In), {
		Position = UDim2.new(0.5, -400, 1, 0)
	}).Completed:Connect(function()
		settingsWindow.Visible = false
	end)
end

return SettingsUI
