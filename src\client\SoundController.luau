-- SoundController.luau
-- Client-side sound management and UI integration

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")
local GuiService = game:GetService("GuiService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

-- Wait for shared modules
local SoundManager = require(ReplicatedStorage:WaitForChild("Shared"):WaitForChild("SoundManager"))

-- Wait for RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = {
	ShowNotification = Assets:WaitForChild("ShowNotification"),
	BuildingPlaced = Assets:WaitForChild("BuildingPlaced"),
	BuildingUpgraded = Assets:WaitForChild("BuildingUpgraded"),
	PlotClaimed = Assets:WaitForChild("PlotClaimed"),
	PlotReleased = Assets:WaitF<PERSON><PERSON>hild("PlotReleased")
}

local SoundController = {}

-- Sound state
local currentGameState = "MENU"
local soundSettings = {
	masterVolume = 0.5,
	musicVolume = 0.3,
	sfxVolume = 0.7,
	uiVolume = 0.5,
	ambientVolume = 0.4,
	muted = false
}

-- Initialize sound controller
function SoundController.Initialize()
	print("🔊 Initializing Sound Controller...")
	
	-- Initialize sound manager
	SoundManager.Initialize()
	
	-- Start with main theme
	SoundManager.PlayMusic("MAIN_THEME")
	
	-- Start city ambience
	SoundManager.PlayAmbient("CITY_AMBIENCE")
	
	-- Setup UI sound integration
	SoundController.SetupUIIntegration()
	
	-- Setup game event sounds
	SoundController.SetupGameEventSounds()
	
	-- Setup input handling
	SoundController.SetupInputHandling()
	
	print("✅ Sound Controller initialized!")
end

-- Setup UI sound integration
function SoundController.SetupUIIntegration()
	-- Wait for main UI to load
	task.spawn(function()
		local urbanSimUI = playerGui:WaitForChild("UrbanSimUI", 10)
		if not urbanSimUI then
			warn("🔊 UrbanSimUI not found for sound integration")
			return
		end
		
		-- Add sounds to all buttons
		SoundController.AddSoundsToButtons(urbanSimUI)
		
		-- Monitor for new UI elements
		urbanSimUI.DescendantAdded:Connect(function(descendant)
			if descendant:IsA("TextButton") or descendant:IsA("ImageButton") then
				SoundController.AddSoundToButton(descendant)
			end
		end)
		
		print("🔊 UI sound integration complete!")
	end)
end

-- Add sounds to all buttons in a container
function SoundController.AddSoundsToButtons(container)
	for _, descendant in ipairs(container:GetDescendants()) do
		if descendant:IsA("TextButton") or descendant:IsA("ImageButton") then
			SoundController.AddSoundToButton(descendant)
		end
	end
end

-- Add sound to a specific button
function SoundController.AddSoundToButton(button)
	-- Hover sound
	button.MouseEnter:Connect(function()
		SoundManager.PlayUI("BUTTON_HOVER")
	end)
	
	-- Click sound
	button.MouseButton1Click:Connect(function()
		SoundManager.PlayUI("BUTTON_CLICK")
	end)
end

-- Setup game event sounds
function SoundController.SetupGameEventSounds()
	-- Building placed
	RemoteEvents.BuildingPlaced.OnClientEvent:Connect(function(buildingData)
		SoundManager.PlaySFX("BUILDING_PLACE")
		print("🔊 Building placed sound triggered")
	end)

	-- Building upgraded
	RemoteEvents.BuildingUpgraded.OnClientEvent:Connect(function(buildingData)
		SoundManager.PlaySFX("BUILDING_UPGRADE")
		print("🔊 Building upgrade sound triggered")
	end)

	-- Plot claimed
	RemoteEvents.PlotClaimed.OnClientEvent:Connect(function(plotNumber, playerName)
		if playerName == player.Name then
			SoundManager.PlaySFX("ACHIEVEMENT")
		else
			SoundManager.PlaySFX("NOTIFICATION")
		end
		print("🔊 Plot claimed sound triggered")
	end)

	-- Plot released
	RemoteEvents.PlotReleased.OnClientEvent:Connect(function(plotNumber, playerName)
		SoundManager.PlayUI("NOTIFICATION")
		print("🔊 Plot released sound triggered")
	end)

	-- Notifications
	RemoteEvents.ShowNotification.OnClientEvent:Connect(function(notificationType, message)
		if notificationType == "Success" then
			SoundManager.PlayUI("NOTIFICATION")
		elseif notificationType == "Error" then
			SoundManager.PlaySFX("ERROR")
		elseif notificationType == "Info" then
			SoundManager.PlayUI("NOTIFICATION")
		end
	end)

	-- Setup sound events from server
	SoundController.SetupSoundEvents()
end

-- Setup sound events from server
function SoundController.SetupSoundEvents()
	task.spawn(function()
		-- Wait for sound events folder
		local soundEventsFolder = Assets:WaitForChild("SoundEvents", 10)
		if not soundEventsFolder then
			warn("🔊 SoundEvents folder not found")
			return
		end

		-- Building placed sound event
		local buildingPlacedEvent = soundEventsFolder:WaitForChild("BuildingPlaced", 5)
		if buildingPlacedEvent then
			buildingPlacedEvent.OnClientEvent:Connect(function(data)
				if data.positional and data.position then
					local position = Vector3.new(data.position.X, data.position.Y, data.position.Z)
					SoundManager.PlaySFX("BUILDING_PLACE", position)
				else
					SoundManager.PlaySFX("BUILDING_PLACE")
				end
				print("🔊 Building placed sound event received")
			end)
		end

		-- Building upgraded sound event
		local buildingUpgradedEvent = soundEventsFolder:WaitForChild("BuildingUpgraded", 5)
		if buildingUpgradedEvent then
			buildingUpgradedEvent.OnClientEvent:Connect(function(data)
				SoundManager.PlaySFX("BUILDING_UPGRADE")
				print("🔊 Building upgraded sound event received")
			end)
		end

		-- Building demolished sound event
		local buildingDemolishedEvent = soundEventsFolder:WaitForChild("BuildingDemolished", 5)
		if buildingDemolishedEvent then
			buildingDemolishedEvent.OnClientEvent:Connect(function(data)
				if data.positional and data.position then
					local position = Vector3.new(data.position.X, data.position.Y, data.position.Z)
					SoundManager.PlaySFX("BUILDING_DEMOLISH", position)
				else
					SoundManager.PlaySFX("BUILDING_DEMOLISH")
				end
				print("🔊 Building demolished sound event received")
			end)
		end

		-- Money earned sound event
		local moneyEarnedEvent = soundEventsFolder:WaitForChild("MoneyEarned", 5)
		if moneyEarnedEvent then
			moneyEarnedEvent.OnClientEvent:Connect(function(data)
				SoundManager.PlaySFX("MONEY_EARN")
				print("🔊 Money earned sound event received")
			end)
		end

		-- Level up sound event
		local levelUpEvent = soundEventsFolder:WaitForChild("LevelUp", 5)
		if levelUpEvent then
			levelUpEvent.OnClientEvent:Connect(function(data)
				SoundManager.PlaySFX("LEVEL_UP")
				print("🔊 Level up sound event received")
			end)
		end

		-- Achievement unlocked sound event
		local achievementEvent = soundEventsFolder:WaitForChild("AchievementUnlocked", 5)
		if achievementEvent then
			achievementEvent.OnClientEvent:Connect(function(data)
				SoundManager.PlaySFX("ACHIEVEMENT")
				print("🔊 Achievement unlocked sound event received")
			end)
		end

		-- Resource collected sound event
		local resourceCollectedEvent = soundEventsFolder:WaitForChild("ResourceCollected", 5)
		if resourceCollectedEvent then
			resourceCollectedEvent.OnClientEvent:Connect(function(data)
				SoundManager.PlaySFX("RESOURCE_COLLECT")
				print("🔊 Resource collected sound event received")
			end)
		end

		-- Plot claimed sound event
		local plotClaimedEvent = soundEventsFolder:WaitForChild("PlotClaimed", 5)
		if plotClaimedEvent then
			plotClaimedEvent.OnClientEvent:Connect(function(data)
				if data.isOwner then
					SoundManager.PlaySFX("ACHIEVEMENT")
				else
					SoundManager.PlayUI("NOTIFICATION")
				end
				print("🔊 Plot claimed sound event received")
			end)
		end

		-- Plot released sound event
		local plotReleasedEvent = soundEventsFolder:WaitForChild("PlotReleased", 5)
		if plotReleasedEvent then
			plotReleasedEvent.OnClientEvent:Connect(function(data)
				SoundManager.PlayUI("NOTIFICATION")
				print("🔊 Plot released sound event received")
			end)
		end

		-- Notification sound event
		local notificationEvent = soundEventsFolder:WaitForChild("Notification", 5)
		if notificationEvent then
			notificationEvent.OnClientEvent:Connect(function(data)
				if data.type == "Success" then
					SoundManager.PlayUI("NOTIFICATION")
				elseif data.type == "Error" then
					SoundManager.PlaySFX("ERROR")
				elseif data.type == "Info" then
					SoundManager.PlayUI("NOTIFICATION")
				end
				print("🔊 Notification sound event received")
			end)
		end

		-- Ambient change sound event
		local ambientChangeEvent = soundEventsFolder:WaitForChild("AmbientChange", 5)
		if ambientChangeEvent then
			ambientChangeEvent.OnClientEvent:Connect(function(data)
				if data.position then
					local position = Vector3.new(data.position.X, data.position.Y, data.position.Z)
					SoundManager.PlayAmbient(data.ambientType, position)
				else
					SoundManager.PlayAmbient(data.ambientType)
				end
				print("🔊 Ambient change sound event received")
			end)
		end

		print("🔊 Sound events setup complete!")
	end)
end

-- Setup input handling for sound controls
function SoundController.SetupInputHandling()
	UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		-- Mute toggle (M key)
		if input.KeyCode == Enum.KeyCode.M then
			SoundController.ToggleMute()
		end
		
		-- Volume controls
		if input.KeyCode == Enum.KeyCode.Equals or input.KeyCode == Enum.KeyCode.Plus then
			SoundController.AdjustMasterVolume(0.1)
		elseif input.KeyCode == Enum.KeyCode.Minus then
			SoundController.AdjustMasterVolume(-0.1)
		end
	end)
end

-- Toggle mute
function SoundController.ToggleMute()
	soundSettings.muted = not soundSettings.muted
	SoundManager.SetMuted(soundSettings.muted)
	
	-- Show notification
	local message = soundSettings.muted and "🔇 Sounds Muted" or "🔊 Sounds Unmuted"
	-- Don't use RemoteEvent to avoid recursion
	print("🔊", message)
end

-- Adjust master volume
function SoundController.AdjustMasterVolume(delta)
	soundSettings.masterVolume = math.clamp(soundSettings.masterVolume + delta, 0, 1)
	SoundManager.SetMasterVolume(soundSettings.masterVolume)
	
	print("🔊 Master volume:", math.floor(soundSettings.masterVolume * 100) .. "%")
end

-- Change game state music
function SoundController.SetGameState(newState)
	if currentGameState == newState then return end
	
	currentGameState = newState
	
	-- Change music based on game state
	if newState == "BUILDING" then
		SoundManager.PlayMusic("BUILD_THEME")
	elseif newState == "MENU" then
		SoundManager.PlayMusic("MENU_THEME")
	else
		SoundManager.PlayMusic("MAIN_THEME")
	end
	
	print("🔊 Game state changed to:", newState)
end

-- Play contextual sounds
function SoundController.PlayContextualSound(context, data)
	if context == "MONEY_EARNED" then
		SoundManager.PlaySFX("MONEY_EARN")
	elseif context == "RESOURCE_COLLECTED" then
		SoundManager.PlaySFX("RESOURCE_COLLECT")
	elseif context == "LEVEL_UP" then
		SoundManager.PlaySFX("LEVEL_UP")
	elseif context == "ACHIEVEMENT" then
		SoundManager.PlaySFX("ACHIEVEMENT")
	elseif context == "WINDOW_OPEN" then
		SoundManager.PlayUI("WINDOW_OPEN")
	elseif context == "WINDOW_CLOSE" then
		SoundManager.PlayUI("WINDOW_CLOSE")
	elseif context == "TAB_SWITCH" then
		SoundManager.PlayUI("TAB_SWITCH")
	elseif context == "BUILDING_DEMOLISH" then
		SoundManager.PlaySFX("BUILDING_DEMOLISH", data and data.position)
	end
end

-- Create sound settings UI
function SoundController.CreateSoundSettingsUI()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then
		warn("🔊 UrbanSimUI not found for sound settings")
		return
	end
	
	-- Create sound settings window
	local soundWindow = Instance.new("Frame")
	soundWindow.Name = "SoundSettings"
	soundWindow.Size = UDim2.new(0, 300, 0, 400)
	soundWindow.Position = UDim2.new(0.5, -150, 0.5, -200)
	soundWindow.BackgroundColor3 = Color3.new(0.1, 0.1, 0.15)
	soundWindow.BorderSizePixel = 0
	soundWindow.Visible = false
	soundWindow.Parent = screenGui
	
	-- Add corner radius
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = soundWindow
	
	-- Title
	local title = Instance.new("TextLabel")
	title.Name = "Title"
	title.Size = UDim2.new(1, 0, 0, 50)
	title.Position = UDim2.new(0, 0, 0, 0)
	title.BackgroundColor3 = Color3.new(0.15, 0.15, 0.2)
	title.Text = "🔊 Sound Settings"
	title.TextColor3 = Color3.new(1, 1, 1)
	title.TextSize = 18
	title.Font = Enum.Font.SourceSansBold
	title.Parent = soundWindow
	
	local titleCorner = Instance.new("UICorner")
	titleCorner.CornerRadius = UDim.new(0, 12)
	titleCorner.Parent = title
	
	-- Volume sliders would go here
	-- (Implementation would require custom slider components)
	
	-- Close button
	local closeButton = Instance.new("TextButton")
	closeButton.Name = "CloseButton"
	closeButton.Size = UDim2.new(0, 30, 0, 30)
	closeButton.Position = UDim2.new(1, -40, 0, 10)
	closeButton.BackgroundColor3 = Color3.new(0.8, 0.2, 0.2)
	closeButton.Text = "×"
	closeButton.TextColor3 = Color3.new(1, 1, 1)
	closeButton.TextSize = 18
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.Parent = title
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton
	
	-- Close functionality
	closeButton.MouseButton1Click:Connect(function()
		soundWindow.Visible = false
		SoundManager.PlayUI("WINDOW_CLOSE")
	end)
	
	-- Add sound to close button
	SoundController.AddSoundToButton(closeButton)
	
	return soundWindow
end

-- Show/hide sound settings
function SoundController.ToggleSoundSettings()
	local screenGui = playerGui:FindFirstChild("UrbanSimUI")
	if not screenGui then return end
	
	local soundWindow = screenGui:FindFirstChild("SoundSettings")
	if not soundWindow then
		soundWindow = SoundController.CreateSoundSettingsUI()
	end
	
	if soundWindow.Visible then
		soundWindow.Visible = false
		SoundManager.PlayUI("WINDOW_CLOSE")
	else
		soundWindow.Visible = true
		SoundManager.PlayUI("WINDOW_OPEN")
	end
end

-- Get current sound settings
function SoundController.GetSoundSettings()
	return soundSettings
end

-- Apply sound settings
function SoundController.ApplySoundSettings(newSettings)
	for key, value in pairs(newSettings) do
		if soundSettings[key] ~= nil then
			soundSettings[key] = value
		end
	end
	
	-- Apply to sound manager
	SoundManager.SetMasterVolume(soundSettings.masterVolume)
	SoundManager.SetCategoryVolume("Music", soundSettings.musicVolume)
	SoundManager.SetCategoryVolume("SoundEffects", soundSettings.sfxVolume)
	SoundManager.SetCategoryVolume("UserInterface", soundSettings.uiVolume)
	SoundManager.SetCategoryVolume("Ambient", soundSettings.ambientVolume)
	SoundManager.SetMuted(soundSettings.muted)
	
	print("🔊 Applied sound settings:", soundSettings)
end

-- Auto-initialize
SoundController.Initialize()

return SoundController
