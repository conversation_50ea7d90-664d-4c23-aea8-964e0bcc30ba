--[[
	Daily Rewards Manager
	Server-side management of daily rewards, streaks, and minute bonuses
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local DailyRewards = require(ReplicatedStorage:WaitForChild("Shared"):WaitFor<PERSON>hild("DailyRewards"))
local DataManager = require(script.Parent:WaitForChild("DataManager"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))

local DailyRewardsManager = {}

-- Active minute reward timers
local MinuteTimers = {}

-- Initialize daily rewards for a player
function DailyRewardsManager.InitializePlayer(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	-- Initialize daily reward data if not exists
	if not playerData.LastDailyReward then
		playerData.LastDailyReward = 0
		playerData.DailyStreak = 0
		playerData.MinuteRewardsClaimed = {}
		playerData.SessionStartTime = os.time()
	end
	
	-- Start minute reward timer
	DailyRewardsManager.StartMinuteTimer(player)
	
	-- Check if daily reward is available
	local canClaim, currentDay = DailyRewards.CanClaimDaily(playerData)
	if canClaim then
		-- Notify client about available daily reward
		RemoteEvents.DailyRewardAvailable:FireClient(player, currentDay)
	end
	
	-- Send current streak info
	RemoteEvents.DailyStreakUpdate:FireClient(player, playerData.DailyStreak or 0, currentDay)
end

-- Claim daily reward
function DailyRewardsManager.ClaimDailyReward(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end
	
	local canClaim, currentDay = DailyRewards.CanClaimDaily(playerData)
	if not canClaim then
		return false, "Daily reward already claimed today"
	end
	
	-- Get base reward
	local baseReward = DailyRewards.GetDayReward(currentDay)
	if not baseReward then
		return false, "Invalid reward day"
	end
	
	-- Apply streak multiplier
	local finalReward, multiplier = DailyRewards.ApplyStreakMultiplier(baseReward, currentDay)
	
	-- Add login time bonus
	local timeBonus = DailyRewards.GetLoginTimeBonus(playerData)
	if timeBonus > 0 then
		finalReward.Pieces = (finalReward.Pieces or 0) + timeBonus
	end
	
	-- Give rewards to player
	for currency, amount in pairs(finalReward) do
		if currency ~= "Type" and type(amount) == "number" then
			DataManager.AddToPlayer(player, currency, amount)
		end
	end
	
	-- Update player data
	playerData.LastDailyReward = os.time()
	playerData.DailyStreak = currentDay
	
	-- Notify client
	RemoteEvents.DailyRewardClaimed:FireClient(player, {
		Day = currentDay,
		Reward = finalReward,
		Multiplier = multiplier,
		TimeBonus = timeBonus,
		Type = baseReward.Type
	})
	
	return true, "Daily reward claimed successfully"
end

-- Start minute reward timer for player
function DailyRewardsManager.StartMinuteTimer(player)
	local userId = tostring(player.UserId)
	
	-- Clear existing timer
	if MinuteTimers[userId] then
		MinuteTimers[userId]:Disconnect()
	end
	
	-- Start new timer (check every 30 seconds)
	MinuteTimers[userId] = RunService.Heartbeat:Connect(function()
		if tick() % 30 < 1 then -- Check every 30 seconds
			DailyRewardsManager.CheckMinuteRewards(player)
		end
	end)
end

-- Check and award minute-based rewards
function DailyRewardsManager.CheckMinuteRewards(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	local availableRewards = DailyRewards.CheckMinuteRewards(playerData)
	
	for _, rewardData in ipairs(availableRewards) do
		-- Notify client about available minute reward
		RemoteEvents.MinuteRewardAvailable:FireClient(player, rewardData)
	end
end

-- Claim minute reward
function DailyRewardsManager.ClaimMinuteReward(player, minutes)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end
	
	local reward = DailyRewards.MINUTE_REWARDS[minutes]
	if not reward then
		return false, "Invalid minute reward"
	end
	
	-- Check if can claim
	local currentTime = os.time()
	if not playerData.MinuteRewardsClaimed then
		playerData.MinuteRewardsClaimed = {}
	end
	
	local lastClaimed = playerData.MinuteRewardsClaimed[minutes] or 0
	local timeSinceLastClaim = currentTime - lastClaimed
	
	if timeSinceLastClaim < minutes * 60 then
		return false, "Reward not ready yet"
	end
	
	-- Give rewards
	for currency, amount in pairs(reward) do
		if type(amount) == "number" then
			DataManager.AddToPlayer(player, currency, amount)
		end
	end
	
	-- Update claim time
	playerData.MinuteRewardsClaimed[minutes] = currentTime
	playerData.LastMinuteReward = currentTime
	
	-- Notify client
	RemoteEvents.MinuteRewardClaimed:FireClient(player, {
		Minutes = minutes,
		Reward = reward,
		Type = minutes >= 60 and "Hourly" or "Minute"
	})
	
	return true, "Minute reward claimed successfully"
end

-- Get player's daily reward status
function DailyRewardsManager.GetDailyStatus(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return nil end
	
	local canClaim, currentDay = DailyRewards.CanClaimDaily(playerData)
	local baseReward = DailyRewards.GetDayReward(currentDay)
	local finalReward, multiplier = DailyRewards.ApplyStreakMultiplier(baseReward, currentDay)
	local timeBonus = DailyRewards.GetLoginTimeBonus(playerData)
	local daysUntilNext, nextTier = DailyRewards.GetDaysUntilNextTier(currentDay)
	
	return {
		CanClaim = canClaim,
		CurrentDay = currentDay,
		Streak = playerData.DailyStreak or 0,
		BaseReward = baseReward,
		FinalReward = finalReward,
		Multiplier = multiplier,
		TimeBonus = timeBonus,
		DaysUntilNextTier = daysUntilNext,
		NextTier = nextTier
	}
end

-- Get available minute rewards
function DailyRewardsManager.GetMinuteStatus(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return {} end
	
	return DailyRewards.CheckMinuteRewards(playerData)
end

-- Clean up player timers
function DailyRewardsManager.CleanupPlayer(player)
	local userId = tostring(player.UserId)
	if MinuteTimers[userId] then
		MinuteTimers[userId]:Disconnect()
		MinuteTimers[userId] = nil
	end
end

-- Remote event handlers
RemoteEvents.ClaimDailyReward.OnServerEvent:Connect(function(player)
	local success, message = DailyRewardsManager.ClaimDailyReward(player)
	if not success then
		local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
		if showNotificationEvent then
			showNotificationEvent:FireClient(player, "Error", message)
		end
	end
end)

RemoteEvents.ClaimMinuteReward.OnServerEvent:Connect(function(player, minutes)
	local success, message = DailyRewardsManager.ClaimMinuteReward(player, minutes)
	local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
	if showNotificationEvent then
		if success then
			showNotificationEvent:FireClient(player, "Success", "Bonus reward claimed!")
		else
			showNotificationEvent:FireClient(player, "Error", message)
		end
	end
end)

-- Remote function handlers
task.spawn(function()
	local AssetsFolder = ReplicatedStorage:WaitForChild("Assets")
	local RemoteFunctions = require(AssetsFolder:WaitForChild("RemoteFunctions"))
	
	RemoteFunctions.GetDailyStatus.OnServerInvoke = function(player)
		return DailyRewardsManager.GetDailyStatus(player)
	end
	
	RemoteFunctions.GetMinuteStatus.OnServerInvoke = function(player)
		return DailyRewardsManager.GetMinuteStatus(player)
	end
end)

-- Player events
game.Players.PlayerAdded:Connect(function(player)
	-- Wait for data to load
	task.wait(3)
	DailyRewardsManager.InitializePlayer(player)
end)

game.Players.PlayerRemoving:Connect(function(player)
	DailyRewardsManager.CleanupPlayer(player)
end)

return DailyRewardsManager
