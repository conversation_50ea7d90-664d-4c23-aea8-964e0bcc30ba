--[[
	Gamepass Manager
	Server-side gamepass handling with MarketplaceService integration
]]

local Players = game:GetService("Players")
local MarketplaceService = game:GetService("MarketplaceService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local GamepassSystem = require(ReplicatedStorage:WaitF<PERSON><PERSON>hild("Shared"):Wait<PERSON><PERSON><PERSON>hild("GamepassSystem"))
local DataManager = require(script.Parent:WaitFor<PERSON>hild("DataManager"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))
local RemoteFunctions = require(Assets:WaitForChild("RemoteFunctions"))

local GamepassManager = {}

-- Cache for gamepass ownership to reduce API calls
local gamepassCache = {}
local cacheExpiry = {}
local CACHE_DURATION = 300 -- 5 minutes

-- Initialize gamepass system
function GamepassManager.Initialize()
	print("🎫 Initializing Gamepass Manager...")
	
	-- Setup MarketplaceService events
	MarketplaceService.PromptGamePassPurchaseFinished:Connect(GamepassManager.OnGamepassPurchaseFinished)
	
	-- Setup player events
	Players.PlayerAdded:Connect(GamepassManager.OnPlayerJoined)
	
	print("✅ Gamepass Manager initialized!")
end

-- Handle player joining
function GamepassManager.OnPlayerJoined(player)
	-- Initialize gamepass cache for player
	gamepassCache[player.UserId] = {}
	cacheExpiry[player.UserId] = {}
	
	-- Load player's gamepass data
	task.spawn(function()
		task.wait(2) -- Wait for data to load
		GamepassManager.RefreshPlayerGamepasses(player)
	end)
end

-- Check if player owns a gamepass (with caching)
function GamepassManager.HasGamepass(player, gamepassKey)
	local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
	if not gamepass then return false end
	
	local userId = player.UserId
	local currentTime = tick()
	
	-- Check cache first
	if gamepassCache[userId] and gamepassCache[userId][gamepassKey] then
		local cacheTime = cacheExpiry[userId][gamepassKey] or 0
		if currentTime - cacheTime < CACHE_DURATION then
			return gamepassCache[userId][gamepassKey]
		end
	end
	
	-- Query MarketplaceService
	local success, hasGamepass = pcall(function()
		return MarketplaceService:UserOwnsGamePassAsync(userId, gamepass.Id)
	end)
	
	if success then
		-- Cache the result
		if not gamepassCache[userId] then
			gamepassCache[userId] = {}
			cacheExpiry[userId] = {}
		end
		gamepassCache[userId][gamepassKey] = hasGamepass
		cacheExpiry[userId][gamepassKey] = currentTime
		
		return hasGamepass
	else
		warn("Failed to check gamepass ownership for", player.Name, gamepassKey)
		return false
	end
end

-- Refresh all gamepass data for player
function GamepassManager.RefreshPlayerGamepasses(player)
	local ownedGamepasses = {}
	
	for gamepassKey, gamepass in pairs(GamepassSystem.GAMEPASSES) do
		if GamepassManager.HasGamepass(player, gamepassKey) then
			ownedGamepasses[gamepassKey] = gamepass
		end
	end
	
	-- Update player data
	local playerData = DataManager.GetPlayerData(player)
	if playerData then
		playerData.OwnedGamepasses = ownedGamepasses
		
		-- Apply gamepass effects
		GamepassManager.ApplyGamepassEffects(player, ownedGamepasses)
	end
	
	-- Notify client
	RemoteEvents.GamepassDataUpdate:FireClient(player, ownedGamepasses)
	
	return ownedGamepasses
end

-- Apply gamepass effects to player
function GamepassManager.ApplyGamepassEffects(player, ownedGamepasses)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	-- Reset multipliers
	playerData.Multipliers = playerData.Multipliers or {}
	playerData.Effects = playerData.Effects or {}
	
	-- Calculate multipliers
	local multipliers = GamepassSystem.GetPlayerMultipliers(player)
	playerData.Multipliers.Income = multipliers.Income
	playerData.Multipliers.XP = multipliers.XP
	playerData.Multipliers.BuildSpeed = multipliers.BuildSpeed
	
	-- Apply effects
	local effects = GamepassSystem.GetPlayerEffects(player)
	for effectName, effectValue in pairs(effects) do
		playerData.Effects[effectName] = effectValue
	end
	
	-- Update unlocked buildings
	playerData.UnlockedBuildings = playerData.UnlockedBuildings or {}
	local unlockedBuildings = GamepassSystem.GetUnlockedBuildings(player)
	for buildingType, _ in pairs(unlockedBuildings) do
		playerData.UnlockedBuildings[buildingType] = true
	end
end

-- Handle gamepass purchase completion
function GamepassManager.OnGamepassPurchaseFinished(player, gamepassId, wasPurchased)
	if not wasPurchased then return end
	
	-- Find which gamepass was purchased
	local purchasedGamepass = nil
	local gamepassKey = nil
	
	for key, gamepass in pairs(GamepassSystem.GAMEPASSES) do
		if gamepass.Id == gamepassId then
			purchasedGamepass = gamepass
			gamepassKey = key
			break
		end
	end
	
	if not purchasedGamepass then
		warn("Unknown gamepass purchased:", gamepassId)
		return
	end
	
	print("🎫 Gamepass purchased:", player.Name, gamepassKey)
	
	-- Clear cache for this gamepass
	if gamepassCache[player.UserId] then
		gamepassCache[player.UserId][gamepassKey] = nil
		cacheExpiry[player.UserId][gamepassKey] = nil
	end
	
	-- Give immediate rewards
	if purchasedGamepass.Rewards then
		for currency, amount in pairs(purchasedGamepass.Rewards) do
			DataManager.AddToPlayer(player, currency, amount)
		end
		
		-- Show reward notification
		local rewardText = "🎫 Gamepass Rewards: "
		for currency, amount in pairs(purchasedGamepass.Rewards) do
			rewardText = rewardText .. amount .. " " .. currency .. " "
		end
		RemoteEvents.ShowNotification:FireClient(player, "Success", rewardText)
	end
	
	-- Process included gamepasses
	if purchasedGamepass.Includes then
		for _, includedGamepassKey in ipairs(purchasedGamepass.Includes) do
			-- Mark as owned (for bundle gamepasses)
			local playerData = DataManager.GetPlayerData(player)
			if playerData then
				playerData.OwnedGamepasses = playerData.OwnedGamepasses or {}
				playerData.OwnedGamepasses[includedGamepassKey] = GamepassSystem.GAMEPASSES[includedGamepassKey]
			end
		end
	end
	
	-- Refresh player gamepass data
	GamepassManager.RefreshPlayerGamepasses(player)
	
	-- Show purchase confirmation
	RemoteEvents.ShowNotification:FireClient(player, "Success", 
		"🎉 Thank you for purchasing " .. purchasedGamepass.Name .. "!")
end

-- Prompt gamepass purchase
function GamepassManager.PromptPurchase(player, gamepassKey)
	local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
	if not gamepass then
		return false, "Invalid gamepass"
	end
	
	-- Check if player already owns it
	if GamepassManager.HasGamepass(player, gamepassKey) then
		return false, "Already owned"
	end
	
	-- Prompt purchase
	local success, error = pcall(function()
		MarketplaceService:PromptGamePassPurchase(player, gamepass.Id)
	end)
	
	if success then
		return true, "Purchase prompted"
	else
		warn("Failed to prompt gamepass purchase:", error)
		return false, "Failed to prompt purchase"
	end
end

-- Get player's gamepass data
function GamepassManager.GetPlayerGamepassData(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return {} end
	
	local ownedGamepasses = playerData.OwnedGamepasses or {}
	local multipliers = GamepassSystem.GetPlayerMultipliers(player)
	local effects = GamepassSystem.GetPlayerEffects(player)
	local recommendations = GamepassSystem.GetRecommendedGamepasses(player, playerData)
	
	return {
		Owned = ownedGamepasses,
		Multipliers = multipliers,
		Effects = effects,
		Recommendations = recommendations,
		Categories = GamepassSystem.CATEGORIES
	}
end

-- Get gamepass shop data
function GamepassManager.GetGamepassShopData()
	local shopData = {
		Categories = {},
		Featured = {},
		Popular = {}
	}
	
	-- Organize by categories
	for categoryName, categoryInfo in pairs(GamepassSystem.CATEGORIES) do
		shopData.Categories[categoryName] = {
			Info = categoryInfo,
			Gamepasses = GamepassSystem.GetGamepassesByCategory(categoryName)
		}
	end
	
	-- Featured gamepasses (manually selected)
	shopData.Featured = {
		"GOLDEN_EDITION",
		"VIP_STATUS",
		"DOUBLE_INCOME",
		"PREMIUM_BUILDINGS"
	}
	
	-- Popular gamepasses (based on typical purchases)
	shopData.Popular = {
		"STARTER_PACK",
		"TRIPLE_XP",
		"FAST_BUILD",
		"AUTO_COLLECT"
	}
	
	return shopData
end

-- Apply income multiplier from gamepasses
function GamepassManager.ApplyIncomeMultiplier(player, baseIncome)
	local multipliers = GamepassSystem.GetPlayerMultipliers(player)
	return math.floor(baseIncome * multipliers.Income)
end

-- Apply XP multiplier from gamepasses
function GamepassManager.ApplyXPMultiplier(player, baseXP)
	local multipliers = GamepassSystem.GetPlayerMultipliers(player)
	return math.floor(baseXP * multipliers.XP)
end

-- Check if player has VIP status
function GamepassManager.IsVIP(player)
	return GamepassManager.HasGamepass(player, "VIP_STATUS") or 
		   GamepassManager.HasGamepass(player, "GOLDEN_EDITION")
end

-- Get VIP daily reward
function GamepassManager.GetVIPDailyReward()
	return {
		Pieces = 5000,
		Cash = 25,
		ClesDiamant = 2,
		XP = 500
	}
end

-- Remote function handlers
RemoteFunctions.GetPlayerGamepasses.OnServerInvoke = function(player)
	return GamepassManager.GetPlayerGamepassData(player)
end

RemoteFunctions.GetGamepassShop.OnServerInvoke = function(player)
	return GamepassManager.GetGamepassShopData()
end

RemoteFunctions.HasGamepass.OnServerInvoke = function(player, gamepassKey)
	return GamepassManager.HasGamepass(player, gamepassKey)
end

-- Remote event handlers
RemoteEvents.PurchaseGamepass.OnServerEvent:Connect(function(player, gamepassKey)
	local success, message = GamepassManager.PromptPurchase(player, gamepassKey)
	if not success then
		local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
		if showNotificationEvent then
			showNotificationEvent:FireClient(player, "Error", message)
		end
	end
end)

RemoteEvents.RefreshGamepasses.OnServerEvent:Connect(function(player)
	GamepassManager.RefreshPlayerGamepasses(player)
end)

-- Player cleanup
Players.PlayerRemoving:Connect(function(player)
	gamepassCache[player.UserId] = nil
	cacheExpiry[player.UserId] = nil
end)

-- Initialize on module load
GamepassManager.Initialize()

return GamepassManager
