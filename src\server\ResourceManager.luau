--[[
	Resource Manager
	Handles resource production, crafting, and consumption
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Config = require(ReplicatedStorage:WaitForChild("Shared"):WaitFor<PERSON>hild("Config"))
local CraftingSystem = require(ReplicatedStorage:WaitFor<PERSON>hild("Shared"):WaitForChild("CraftingSystem"))
local DataManager = require(script.Parent:WaitForChild("DataManager"))

-- Get RemoteEvents
local Assets = ReplicatedStorage:WaitForChild("Assets")
local RemoteEvents = require(Assets:WaitForChild("RemoteEvents"))

local ResourceManager = {}

-- Active production timers
local ProductionTimers = {}
local CraftingTimers = {}

-- Start resource production for a building
function ResourceManager.StartProduction(player, buildingId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return false end
	
	local building = playerData.Buildings[buildingId]
	if not building then return false end
	
	local buildingConfig = Config.BUILDINGS[building.Type]
	if not buildingConfig or not buildingConfig.Production then
		return false
	end
	
	-- Create timer key
	local timerKey = player.UserId .. "_" .. buildingId
	
	-- Stop existing timer if any
	if ProductionTimers[timerKey] then
		ProductionTimers[timerKey]:Disconnect()
	end
	
	-- Start production timer
	ProductionTimers[timerKey] = RunService.Heartbeat:Connect(function()
		local now = tick()
		local lastProduction = building.LastProduction or now
		
		if now - lastProduction >= buildingConfig.ProductionTime then
			-- Produce resources
			for resource, amount in pairs(buildingConfig.Production) do
				local currentAmount = playerData.Resources[resource] or 0
				playerData.Resources[resource] = currentAmount + (amount * building.Level)
			end
			
			building.LastProduction = now
			
			-- Notify client
			RemoteEvents.ResourceUpdated:FireClient(player, playerData.Resources)
		end
	end)
	
	return true
end

-- Stop resource production for a building
function ResourceManager.StopProduction(player, buildingId)
	local timerKey = player.UserId .. "_" .. buildingId
	if ProductionTimers[timerKey] then
		ProductionTimers[timerKey]:Disconnect()
		ProductionTimers[timerKey] = nil
		return true
	end
	return false
end

-- Start crafting process (Enhanced)
function ResourceManager.StartCrafting(player, recipe, quantity, slotId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end

	-- Use the enhanced crafting system
	local success, message, craftingJob = CraftingSystem.AddToQueue(playerData, recipe, quantity, slotId)

	if success then
		-- Start crafting timer for this job
		ResourceManager.StartCraftingTimer(craftingJob)

		-- Notify client
		RemoteEvents.ResourceUpdated:FireClient(player, playerData.Resources)
		RemoteEvents.CraftingStarted:FireClient(player, craftingJob)
	end

	return success, message
end

-- Complete crafting from slot
function ResourceManager.CompleteCraftingSlot(player, slotId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end

	local success, message, result = CraftingSystem.CompleteCraftingJob(playerData, slotId)

	if success then
		-- Notify client
		RemoteEvents.ResourceUpdated:FireClient(player, playerData.Resources)
		RemoteEvents.CraftingCompleted:FireClient(player, result.Recipe, result.Quantity)
	end

	return success, message
end

-- Cancel crafting in slot
function ResourceManager.CancelCrafting(player, slotId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end

	local success, message = CraftingSystem.CancelCraftingJob(playerData, slotId)

	if success then
		-- Clean up timer
		local job = playerData.CraftingQueue and playerData.CraftingQueue[slotId]
		if job then
			CraftingTimers[job.Id] = nil
		end

		-- Notify client
		RemoteEvents.ResourceUpdated:FireClient(player, playerData.Resources)
		RemoteEvents.CraftingCancelled:FireClient(player, slotId)
	end

	return success, message
end

-- Speed up crafting with Cash
function ResourceManager.SpeedUpCrafting(player, slotId)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end

	-- Calculate cash cost (1 Cash per minute remaining)
	local job = playerData.CraftingQueue and playerData.CraftingQueue[slotId]
	if not job then
		return false, "No job in this slot"
	end

	local timeRemaining = CraftingSystem.GetTimeRemaining(job)
	local cashCost = math.ceil(timeRemaining / 60)

	local success, message = CraftingSystem.SpeedUpCrafting(playerData, slotId, cashCost)

	if success then
		-- Clean up timer
		CraftingTimers[job.Id] = nil

		-- Notify client
		RemoteEvents.ResourceUpdated:FireClient(player, playerData.Resources)
		RemoteEvents.CraftingCompleted:FireClient(player, job.Recipe, job.Quantity)
	end

	return success, message
end

-- Start crafting timer
function ResourceManager.StartCraftingTimer(craftingData)
	local timerKey = craftingData.Id
	
	CraftingTimers[timerKey] = task.wait(craftingData.Duration)
	
	task.spawn(function()
		task.wait(craftingData.Duration)
		ResourceManager.CompleteCrafting(craftingData)
	end)
end

-- Complete crafting process
function ResourceManager.CompleteCrafting(craftingData)
	-- Find player
	local player = nil
	for _, p in pairs(game.Players:GetPlayers()) do
		if p.UserId == craftingData.Player then
			player = p
			break
		end
	end
	
	if not player then return end
	
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	-- Remove from crafting queue
	for i, craft in ipairs(playerData.CraftingQueue) do
		if craft.Id == craftingData.Id then
			table.remove(playerData.CraftingQueue, i)
			break
		end
	end
	
	-- Add crafted items to inventory
	local recipeConfig = Config.CRAFTING_RECIPES[craftingData.Recipe]
	if recipeConfig then
		local currentAmount = playerData.Resources[craftingData.Recipe] or 0
		playerData.Resources[craftingData.Recipe] = currentAmount + (recipeConfig.Output * craftingData.Quantity)
		
		-- Notify client
		RemoteEvents.CraftingCompleted:FireClient(player, craftingData.Recipe, craftingData.Quantity)
		RemoteEvents.ResourceUpdated:FireClient(player, playerData.Resources)
	end
	
	-- Clean up timer
	CraftingTimers[craftingData.Id] = nil
end

-- Collect taxes from City Hall
function ResourceManager.CollectTaxes(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then
		return false, "Player data not loaded"
	end
	
	-- Check if enough time has passed since last collection
	local now = tick()
	local timeSinceLastCollection = now - (playerData.LastTaxCollection or 0)
	local minInterval = 3600 -- 1 hour in seconds
	
	if timeSinceLastCollection < minInterval then
		local timeLeft = minInterval - timeSinceLastCollection
		return false, "Must wait " .. math.ceil(timeLeft/60) .. " more minutes"
	end
	
	-- Calculate tax amount
	local cityHall = nil
	for _, building in pairs(playerData.Buildings) do
		if building.Type == "CITY_HALL" then
			cityHall = building
			break
		end
	end
	
	if not cityHall then
		return false, "City Hall not found"
	end
	
	local taxRate = Config.BUILDINGS.CITY_HALL.TaxRate
	local taxAmount = playerData.Population * taxRate
	
	-- Add pieces
	DataManager.AddToPlayer(player, "Pieces", taxAmount)
	
	-- Update last collection time
	playerData.LastTaxCollection = now
	
	-- Award XP
	DataManager.AddToPlayer(player, "XP", Config.XP_REWARDS.COLLECT_TAXES)
	
	return true, "Collected " .. taxAmount .. " pieces"
end

-- Get resource production rate
function ResourceManager.GetProductionRate(player, resource)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return 0 end
	
	local totalProduction = 0
	
	for _, building in pairs(playerData.Buildings) do
		if building.Active then
			local config = Config.BUILDINGS[building.Type]
			if config and config.Production and config.Production[resource] then
				local productionPerHour = (config.Production[resource] * building.Level) * (3600 / config.ProductionTime)
				totalProduction += productionPerHour
			end
		end
	end
	
	return totalProduction
end

-- Initialize production for all player buildings
function ResourceManager.InitializePlayerProduction(player)
	local playerData = DataManager.GetPlayerData(player)
	if not playerData then return end
	
	for buildingId, building in pairs(playerData.Buildings) do
		local config = Config.BUILDINGS[building.Type]
		if config and config.Production then
			ResourceManager.StartProduction(player, buildingId)
		end
	end
end

-- Clean up timers when player leaves
function ResourceManager.CleanupPlayerTimers(player)
	local userId = tostring(player.UserId)
	
	-- Clean up production timers
	for timerKey, timer in pairs(ProductionTimers) do
		if string.find(timerKey, userId) then
			timer:Disconnect()
			ProductionTimers[timerKey] = nil
		end
	end
	
	-- Clean up crafting timers
	for timerKey, timer in pairs(CraftingTimers) do
		if string.find(timerKey, userId) then
			CraftingTimers[timerKey] = nil
		end
	end
end

-- Remote event handlers
RemoteEvents.StartCrafting.OnServerEvent:Connect(function(player, recipe, quantity, slotId)
	local success, message = ResourceManager.StartCrafting(player, recipe, quantity, slotId)
	local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
	if showNotificationEvent then
		if not success then
			showNotificationEvent:FireClient(player, "Error", message)
		else
			showNotificationEvent:FireClient(player, "Success", message)
		end
	end
end)

RemoteEvents.CompleteCrafting.OnServerEvent:Connect(function(player, slotId)
	local success, message = ResourceManager.CompleteCraftingSlot(player, slotId)
	local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
	if showNotificationEvent then
		if not success then
			showNotificationEvent:FireClient(player, "Error", message)
		else
			showNotificationEvent:FireClient(player, "Success", message)
		end
	end
end)

RemoteEvents.CancelCrafting.OnServerEvent:Connect(function(player, slotId)
	local success, message = ResourceManager.CancelCrafting(player, slotId)
	local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
	if showNotificationEvent then
		if not success then
			showNotificationEvent:FireClient(player, "Error", message)
		else
			showNotificationEvent:FireClient(player, "Info", message)
		end
	end
end)

RemoteEvents.SpeedUpCrafting.OnServerEvent:Connect(function(player, slotId)
	local success, message = ResourceManager.SpeedUpCrafting(player, slotId)
	local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
	if showNotificationEvent then
		if not success then
			showNotificationEvent:FireClient(player, "Error", message)
		else
			showNotificationEvent:FireClient(player, "Success", message)
		end
	end
end)

RemoteEvents.CollectTaxes.OnServerEvent:Connect(function(player)
	local success, message = ResourceManager.CollectTaxes(player)
	local showNotificationEvent = Assets:FindFirstChild("ShowNotification")
	if showNotificationEvent then
		if success then
			showNotificationEvent:FireClient(player, "Success", message)
		else
			showNotificationEvent:FireClient(player, "Error", message)
		end
	end
end)

-- Player events
game.Players.PlayerAdded:Connect(function(player)
	-- Wait for data to load
	task.wait(2)
	ResourceManager.InitializePlayerProduction(player)
end)

game.Players.PlayerRemoving:Connect(function(player)
	ResourceManager.CleanupPlayerTimers(player)
end)

-- Setup RemoteFunction handlers
task.spawn(function()
	-- Wait for RemoteFunctions to be created
	local AssetsFolder = ReplicatedStorage:WaitForChild("Assets")
	local RemoteFunctions = require(AssetsFolder:WaitForChild("RemoteFunctions"))

	-- Get crafting queue
	RemoteFunctions.GetCraftingQueue.OnServerInvoke = function(player)
		local playerData = DataManager.GetPlayerData(player)
		return playerData and playerData.CraftingQueue or {}
	end

	-- Get available recipes
	RemoteFunctions.GetAvailableRecipes.OnServerInvoke = function(player)
		local playerData = DataManager.GetPlayerData(player)
		if not playerData then return {} end
		return CraftingSystem.GetAvailableRecipes(playerData)
	end

	-- Get max craftable quantity
	RemoteFunctions.GetMaxCraftableQuantity.OnServerInvoke = function(player, recipe)
		local playerData = DataManager.GetPlayerData(player)
		if not playerData then return 0 end
		return CraftingSystem.GetMaxCraftableQuantity(playerData, recipe)
	end
end)

return ResourceManager
