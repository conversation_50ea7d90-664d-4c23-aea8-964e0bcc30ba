--[[
	UrbanSim Configuration
	Central configuration for all game systems
]]

local Config = {}

-- Game Settings
Config.GAME_VERSION = "1.0.0"
Config.MAX_PLAYERS = 30
Config.AUTOSAVE_INTERVAL = 60 -- seconds

-- Currencies
Config.CURRENCIES = {
	PIECES = "Pieces",
	CASH = "Cash", 
	KEYS = "Cles",
	DIAMOND_KEYS = "ClesDiamant",
	GAMMA_COIN = "GammaCoin"
}

-- Starting Values
Config.STARTING_VALUES = {
	Pieces = 1000,
	Cash = 0,
	Cles = 0,
	ClesDiamant = 0,
	GammaCoin = 0,
	XP = 0,
	Population = 0,
	ProductionEnergie = 0,
	ProductionEau = 0
}

-- Building Types
Config.BUILDING_TYPES = {
	RESIDENTIAL = "Residential",
	COMMERCIAL = "Commercial", 
	INDUSTRIAL = "Industrial",
	SERVICE = "Service",
	UTILITY = "Utility",
	DECORATION = "Decoration",
	SPECIAL = "Special"
}

-- Building Categories
Config.BUILDINGS = {
	-- Residential Buildings
	HOUSE_SMALL = {
		Name = "Petite Maison",
		Description = "Une petite maison confortable pour une famille. Fournit un logement de base avec un style traditionnel.",
		Type = Config.BUILDING_TYPES.RESIDENTIAL,
		Cost = {Pieces = 100},
		Population = 4,
		EnergyConsumption = 2,
		WaterConsumption = 1,
		Size = {4, 3, 4}, -- X, Y, Z
		UnlockLevel = 1,
		Icon = "🏠",
		BuildTime = 30, -- seconds
		MaxLevel = 3
	},

	HOUSE_MEDIUM = {
		Name = "Maison Moyenne",
		Description = "Une maison de taille moyenne avec plus d'espace et de confort. Idéale pour les familles grandissantes.",
		Type = Config.BUILDING_TYPES.RESIDENTIAL,
		Cost = {Pieces = 250},
		Population = 8,
		EnergyConsumption = 4,
		WaterConsumption = 2,
		Size = {6, 4, 6},
		UnlockLevel = 5,
		Icon = "🏡",
		BuildTime = 45,
		MaxLevel = 4
	},

	APARTMENT = {
		Name = "Appartement",
		Description = "Un immeuble d'appartements moderne qui peut loger de nombreuses familles dans un espace compact.",
		Type = Config.BUILDING_TYPES.RESIDENTIAL,
		Cost = {Pieces = 500},
		Population = 16,
		EnergyConsumption = 8,
		WaterConsumption = 4,
		Size = {8, 8, 8},
		UnlockLevel = 10,
		Icon = "🏢",
		BuildTime = 90,
		MaxLevel = 5
	},

	MANSION = {
		Name = "Manoir",
		Type = Config.BUILDING_TYPES.RESIDENTIAL,
		Cost = {Pieces = 1000},
		Population = 12,
		EnergyConsumption = 15,
		WaterConsumption = 8,
		Size = {10, 6, 10},
		UnlockLevel = 8
	},

	SKYSCRAPER = {
		Name = "Gratte-ciel",
		Type = Config.BUILDING_TYPES.RESIDENTIAL,
		Cost = {Pieces = 2000},
		Population = 50,
		EnergyConsumption = 30,
		WaterConsumption = 20,
		Size = {12, 20, 12},
		UnlockLevel = 12
	},
	
	-- Utility Buildings
	POWER_PLANT = {
		Name = "Centrale Électrique",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 800},
		EnergyProduction = 50,
		Size = {10, 6, 10},
		UnlockLevel = 3
	},
	
	WATER_PLANT = {
		Name = "Station d'Eau",
		Description = "Une station de traitement d'eau qui fournit de l'eau potable à la ville.",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 600},
		WaterProduction = 30,
		EnergyConsumption = 10,
		Size = {8, 5, 8},
		UnlockLevel = 2,
		Icon = "💧",
		BuildTime = 60,
		MaxLevel = 4
	},

	WATER_TREATMENT = {
		Name = "Station d'Épuration",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 700},
		WaterProduction = 40,
		WaterTreatment = 25,
		Size = {10, 4, 8},
		UnlockLevel = 6
	},

	SOLAR_PLANT = {
		Name = "Centrale Solaire",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 1000},
		EnergyProduction = 35,
		EcoFriendly = true,
		Size = {12, 2, 12},
		UnlockLevel = 7
	},

	WIND_TURBINE = {
		Name = "Éolienne",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 400},
		EnergyProduction = 15,
		EcoFriendly = true,
		Size = {4, 12, 4},
		UnlockLevel = 5
	},

	GARBAGE_DUMP = {
		Name = "Décharge",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 300},
		WasteManagement = 20,
		CoverageRadius = 30,
		Size = {8, 3, 8},
		UnlockLevel = 4
	},

	RECYCLING_CENTER = {
		Name = "Centre de Recyclage",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 500},
		WasteManagement = 35,
		ResourceGeneration = {Metal = 0.5, Plastic = 0.5},
		CoverageRadius = 40,
		Size = {8, 4, 6},
		UnlockLevel = 6
	},

	WASTE_MANAGEMENT = {
		Name = "Gestion des Déchets",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 600},
		WasteManagement = 50,
		CoverageRadius = 60,
		Size = {10, 4, 8},
		UnlockLevel = 5
	},
	
	-- Service Buildings
	POLICE_STATION = {
		Name = "Commissariat",
		Type = Config.BUILDING_TYPES.SERVICE,
		Cost = {Pieces = 400},
		CoverageRadius = 50,
		Size = {6, 4, 6},
		UnlockLevel = 4
	},

	FIRE_STATION = {
		Name = "Caserne de Pompiers",
		Type = Config.BUILDING_TYPES.SERVICE,
		Cost = {Pieces = 350},
		CoverageRadius = 45,
		Size = {6, 4, 8},
		UnlockLevel = 3
	},

	HOSPITAL = {
		Name = "Hôpital",
		Type = Config.BUILDING_TYPES.SERVICE,
		Cost = {Pieces = 600},
		CoverageRadius = 60,
		Size = {10, 5, 8},
		UnlockLevel = 6
	},

	SCHOOL = {
		Name = "École",
		Type = Config.BUILDING_TYPES.SERVICE,
		Cost = {Pieces = 450},
		CoverageRadius = 40,
		EducationBonus = 15,
		Size = {8, 4, 6},
		UnlockLevel = 5
	},

	LIBRARY = {
		Name = "Bibliothèque",
		Type = Config.BUILDING_TYPES.SERVICE,
		Cost = {Pieces = 300},
		CoverageRadius = 35,
		EducationBonus = 10,
		Size = {6, 3, 6},
		UnlockLevel = 4
	},

	UNIVERSITY = {
		Name = "Université",
		Type = Config.BUILDING_TYPES.SERVICE,
		Cost = {Pieces = 800},
		CoverageRadius = 60,
		EducationBonus = 25,
		Size = {12, 6, 10},
		UnlockLevel = 8
	},
	
	-- Commercial Buildings
	SHOP_SMALL = {
		Name = "Petit Magasin",
		Description = "Un petit magasin de quartier offrant des produits de base. Génère des revenus réguliers.",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 200},
		Revenue = 15,
		RevenueTime = 60,
		EnergyConsumption = 3,
		WaterConsumption = 1,
		Size = {4, 3, 4},
		UnlockLevel = 2,
		Icon = "🏪",
		BuildTime = 30,
		MaxLevel = 3
	},

	SHOP_MEDIUM = {
		Name = "Magasin Moyen",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 400},
		Revenue = 30,
		RevenueTime = 60,
		Size = {6, 4, 6},
		UnlockLevel = 4
	},

	SHOP_LARGE = {
		Name = "Grand Magasin",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 700},
		Revenue = 50,
		RevenueTime = 60,
		Size = {8, 5, 8},
		UnlockLevel = 6
	},

	OFFICE = {
		Name = "Bureau",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 500},
		Revenue = 35,
		RevenueTime = 60,
		Size = {6, 8, 6},
		UnlockLevel = 5
	},

	MALL = {
		Name = "Centre Commercial",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 1200},
		Revenue = 80,
		RevenueTime = 60,
		Size = {12, 6, 10},
		UnlockLevel = 8
	},

	BANK = {
		Name = "Banque",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 800},
		Revenue = 50,
		RevenueTime = 60,
		TaxBonus = 10,
		Size = {8, 6, 8},
		UnlockLevel = 6
	},

	RESTAURANT = {
		Name = "Restaurant",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 300},
		Revenue = 20,
		RevenueTime = 45,
		Size = {6, 3, 6},
		UnlockLevel = 3
	},

	HOTEL = {
		Name = "Hôtel",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 600},
		Revenue = 40,
		RevenueTime = 60,
		TourismBonus = 15,
		Size = {8, 8, 6},
		UnlockLevel = 5
	},

	GAS_STATION = {
		Name = "Station-Service",
		Type = Config.BUILDING_TYPES.COMMERCIAL,
		Cost = {Pieces = 250},
		Revenue = 18,
		RevenueTime = 30,
		Size = {6, 3, 8},
		UnlockLevel = 3
	},

	-- Industrial Buildings
	METAL_FACTORY = {
		Name = "Usine de Métal",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 300},
		Production = {Metal = 1},
		ProductionTime = 30, -- seconds
		Size = {8, 4, 6},
		UnlockLevel = 2
	},

	PLASTIC_FACTORY = {
		Name = "Usine de Plastique",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 350},
		Production = {Plastic = 1},
		ProductionTime = 35,
		Size = {8, 4, 6},
		UnlockLevel = 3
	},

	WOOD_FACTORY = {
		Name = "Scierie",
		Description = "Une scierie moderne qui transforme les arbres en bois de construction de qualité.",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 280},
		Production = {Wood = 1},
		ProductionTime = 25,
		EnergyConsumption = 8,
		WaterConsumption = 2,
		Size = {8, 4, 6},
		UnlockLevel = 2,
		Icon = "🪵",
		BuildTime = 45,
		MaxLevel = 4
	},

	ELECTRONICS_FACTORY = {
		Name = "Usine d'Électronique",
		Description = "Une usine high-tech spécialisée dans la production de composants électroniques avancés.",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 500},
		Production = {CarteMere = 1},
		ProductionTime = 60,
		EnergyConsumption = 15,
		WaterConsumption = 3,
		Size = {10, 4, 8},
		UnlockLevel = 4,
		Icon = "🔌",
		BuildTime = 90,
		MaxLevel = 5
	},

	TECH_FACTORY = {
		Name = "Usine High-Tech",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 800},
		Production = {PC = 1},
		ProductionTime = 180,
		Size = {12, 5, 10},
		UnlockLevel = 7
	},

	FACTORY_SMALL = {
		Name = "Petite Usine",
		Description = "Une petite usine polyvalente capable de produire différents matériaux de base.",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 200},
		Production = {Metal = 0.5, Plastic = 0.5},
		ProductionTime = 45,
		EnergyConsumption = 12,
		WaterConsumption = 4,
		Size = {6, 3, 6},
		UnlockLevel = 1,
		Icon = "🏭",
		BuildTime = 60,
		MaxLevel = 3
	},

	FACTORY_LARGE = {
		Name = "Grande Usine",
		Description = "Une grande usine industrielle avec une capacité de production élevée et des technologies avancées.",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 600},
		Production = {Metal = 2, Plastic = 1},
		ProductionTime = 60,
		EnergyConsumption = 25,
		WaterConsumption = 8,
		Size = {10, 5, 8},
		UnlockLevel = 5,
		Icon = "🏭",
		BuildTime = 120,
		MaxLevel = 5
	},

	WAREHOUSE = {
		Name = "Entrepôt",
		Type = Config.BUILDING_TYPES.INDUSTRIAL,
		Cost = {Pieces = 400},
		Storage = 100,
		Size = {8, 4, 12},
		UnlockLevel = 3
	},
	
	-- Decoration Buildings
	PARK_SMALL = {
		Name = "Petit Parc",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 150},
		HappinessBonus = 10,
		CoverageRadius = 25,
		Size = {6, 1, 6},
		UnlockLevel = 2
	},

	PARK_MEDIUM = {
		Name = "Parc Moyen",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 300},
		HappinessBonus = 20,
		CoverageRadius = 35,
		Size = {8, 1, 8},
		UnlockLevel = 4
	},

	PARK_LARGE = {
		Name = "Grand Parc",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 600},
		HappinessBonus = 35,
		CoverageRadius = 50,
		Size = {12, 1, 12},
		UnlockLevel = 6
	},

	FOUNTAIN = {
		Name = "Fontaine",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 200},
		HappinessBonus = 15,
		CoverageRadius = 20,
		Size = {4, 3, 4},
		UnlockLevel = 3
	},

	STATUE = {
		Name = "Statue",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 400},
		HappinessBonus = 25,
		CoverageRadius = 30,
		Size = {3, 6, 3},
		UnlockLevel = 5
	},

	PLAYGROUND = {
		Name = "Aire de Jeux",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 250},
		HappinessBonus = 20,
		CoverageRadius = 30,
		Size = {6, 2, 6},
		UnlockLevel = 3
	},

	GARDEN = {
		Name = "Jardin",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 100},
		HappinessBonus = 8,
		CoverageRadius = 15,
		Size = {4, 1, 4},
		UnlockLevel = 1
	},

	MONUMENT = {
		Name = "Monument",
		Type = Config.BUILDING_TYPES.DECORATION,
		Cost = {Pieces = 800},
		HappinessBonus = 40,
		CoverageRadius = 60,
		TourismBonus = 20,
		Size = {6, 10, 6},
		UnlockLevel = 8
	},

	-- Infrastructure
	ROAD = {
		Name = "Route",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 10},
		Size = {4, 1, 4},
		UnlockLevel = 1,
		IsRoad = true
	},

	BRIDGE = {
		Name = "Pont",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 50},
		Size = {4, 2, 12},
		UnlockLevel = 3,
		IsRoad = true
	},

	TUNNEL = {
		Name = "Tunnel",
		Type = Config.BUILDING_TYPES.UTILITY,
		Cost = {Pieces = 100},
		Size = {4, 1, 8},
		UnlockLevel = 5,
		IsRoad = true
	},

	-- Special Buildings
	CITY_HALL = {
		Name = "Hôtel de Ville",
		Type = Config.BUILDING_TYPES.SPECIAL,
		Cost = {Pieces = 0}, -- Free starter building
		Size = {12, 8, 12},
		UnlockLevel = 1,
		TaxRate = 10 -- Pieces per population per collection
	},

	AIRPORT = {
		Name = "Aéroport",
		Type = Config.BUILDING_TYPES.SPECIAL,
		Cost = {Pieces = 2000},
		TourismBonus = 50,
		Size = {20, 4, 16},
		UnlockLevel = 10
	},

	STADIUM = {
		Name = "Stade",
		Type = Config.BUILDING_TYPES.SPECIAL,
		Cost = {Pieces = 1500},
		HappinessBonus = 60,
		CoverageRadius = 80,
		Size = {16, 8, 12},
		UnlockLevel = 9
	},

	LIGHTHOUSE = {
		Name = "Phare",
		Type = Config.BUILDING_TYPES.SPECIAL,
		Cost = {Pieces = 600},
		TourismBonus = 25,
		Size = {4, 15, 4},
		UnlockLevel = 7
	}
}

-- Resources for Crafting
Config.RESOURCES = {
	METAL = "Metal",
	PLASTIC = "Plastic", 
	WOOD = "Wood",
	MOTHERBOARD = "CarteMere",
	METER = "Metre",
	PC = "PC"
}

-- Crafting Recipes
Config.CRAFTING_RECIPES = {
	CarteMere = {
		Ingredients = {Metal = 1},
		Time = 60,
		Output = 1
	},
	
	Metre = {
		Ingredients = {Metal = 1, Plastic = 1},
		Time = 90,
		Output = 1
	},
	
	PC = {
		Ingredients = {CarteMere = 1, Metre = 1, Metal = 1},
		Time = 180,
		Output = 1
	}
}

-- XP System
Config.XP_REWARDS = {
	BUILD_BUILDING = 10,
	UPGRADE_BUILDING = 25,
	COMPLETE_MISSION = 50,
	COLLECT_TAXES = 5
}

Config.LEVEL_REQUIREMENTS = {
	[1] = 0,
	[2] = 100,
	[3] = 250,
	[4] = 500,
	[5] = 1000,
	[6] = 1750,
	[7] = 2750,
	[8] = 4000,
	[9] = 5500,
	[10] = 7500
}

-- Gamepass IDs (placeholder - replace with actual IDs)
Config.GAMEPASSES = {
	MONEY_X2 = 123456,
	SERVICE_BOOST_X2 = 123457,
	QUEUE_PRO = 123458,
	CONSTRUCTION_PRO = 123459,
	PREMIUM_PACK = 123460,
	GAMMA_COIN_X2 = 123461
}

-- Grid System
Config.GRID_SIZE = 4 -- Size of each grid cell
Config.ROAD_WIDTH = 4

-- Zone Expansion
Config.ZONE_EXPANSION = {
	STARTING_SIZE = {20, 20}, -- Grid cells
	EXPANSION_COST_BASE = 100,
	EXPANSION_COST_MULTIPLIER = 1.5,
	SPECIAL_OBJECTS = {
		"Engrenage",
		"CasqueChantier", 
		"Bouton"
	}
}

-- Mission System
Config.MISSIONS = {
	DISASTER_INTERVAL = {300, 600}, -- Min/Max seconds between disasters
	DISASTER_TYPES = {
		"Tornado",
		"Fire",
		"Earthquake",
		"Robot_Invasion"
	},
	REPAIR_COSTS = {
		Tornado = {Metal = 2, Plastic = 1},
		Fire = {Wood = 3, Metal = 1},
		Earthquake = {Metal = 3, Plastic = 2},
		Robot_Invasion = {PC = 1, Metal = 2}
	}
}

-- Service Coverage
Config.SERVICE_COVERAGE = {
	POLICE = 50,
	FIRE = 45,
	HOSPITAL = 60,
	GARBAGE = 40,
	SEWAGE = 35
}

return Config
