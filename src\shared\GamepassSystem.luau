--[[
	Gamepass System
	Comprehensive gamepass management with marketplace integration
]]

local GamepassSystem = {}

-- Gamepass definitions with real marketplace IDs
GamepassSystem.GAMEPASSES = {
	-- Premium Currency Packs
	STARTER_PACK = {
		Id = 123456789, -- Replace with actual gamepass ID
		Name = "Starter Pack",
		Description = "Get 10,000 Pieces + 50 Cash + 5 Diamond Keys to jumpstart your city!",
		Icon = "💎",
		Price = 25, -- Robux
		Category = "Currency",
		Rewards = {
			Pieces = 10000,
			Cash = 50,
			ClesDiamant = 5
		},
		OneTime = true
	},
	
	MEGA_PACK = {
		Id = 123456790, -- Replace with actual gamepass ID
		Name = "Mega Pack",
		Description = "Massive boost! 100,000 Pieces + 500 Cash + 25 Diamond Keys + 10 Gamma Coins",
		Icon = "🚀",
		Price = 99, -- Robux
		Category = "Currency",
		Rewards = {
			Pieces = 100000,
			Cash = 500,
			ClesDiamant = 25,
			GammaCoin = 10
		},
		OneTime = true
	},
	
	-- Building Unlocks
	PREMIUM_BUILDINGS = {
		Id = 123456791, -- Replace with actual gamepass ID
		Name = "Premium Buildings",
		Description = "Unlock exclusive premium buildings: Skyscraper, Luxury Mall, Space Center!",
		Icon = "🏗️",
		Price = 199, -- Robux
		Category = "Buildings",
		Unlocks = {
			"SKYSCRAPER_PREMIUM",
			"LUXURY_MALL",
			"SPACE_CENTER",
			"GOLDEN_STATUE"
		},
		Permanent = true
	},
	
	INDUSTRIAL_PACK = {
		Id = 123456792, -- Replace with actual gamepass ID
		Name = "Industrial Pack",
		Description = "Unlock advanced industrial buildings for maximum production!",
		Icon = "🏭",
		Price = 149, -- Robux
		Category = "Buildings",
		Unlocks = {
			"MEGA_FACTORY",
			"NUCLEAR_PLANT",
			"MINING_COMPLEX",
			"TECH_CENTER"
		},
		Permanent = true
	},
	
	-- Multipliers & Boosts
	DOUBLE_INCOME = {
		Id = *********, -- Replace with actual gamepass ID
		Name = "Double Income",
		Description = "Permanently double all income from buildings and taxes!",
		Icon = "💰",
		Price = 299, -- Robux
		Category = "Multipliers",
		Effects = {
			IncomeMultiplier = 2.0
		},
		Permanent = true
	},
	
	TRIPLE_XP = {
		Id = *********, -- Replace with actual gamepass ID
		Name = "Triple XP",
		Description = "Gain 3x XP from all activities! Level up faster than ever!",
		Icon = "⭐",
		Price = 199, -- Robux
		Category = "Multipliers",
		Effects = {
			XPMultiplier = 3.0
		},
		Permanent = true
	},
	
	FAST_BUILD = {
		Id = *********, -- Replace with actual gamepass ID
		Name = "Instant Build",
		Description = "All buildings construct instantly! No more waiting!",
		Icon = "⚡",
		Price = 249, -- Robux
		Category = "Convenience",
		Effects = {
			InstantBuild = true
		},
		Permanent = true
	},
	
	-- VIP Features
	VIP_STATUS = {
		Id = *********, -- Replace with actual gamepass ID
		Name = "VIP Status",
		Description = "VIP perks: Daily VIP rewards, exclusive chat tag, priority support!",
		Icon = "👑",
		Price = 399, -- Robux
		Category = "VIP",
		Effects = {
			VIPStatus = true,
			DailyVIPReward = true,
			ChatTag = "[VIP]",
			PrioritySupport = true
		},
		Permanent = true
	},
	
	-- Utility & Convenience
	UNLIMITED_STORAGE = {
		Id = 123456797, -- Replace with actual gamepass ID
		Name = "Unlimited Storage",
		Description = "Never worry about inventory space again! Unlimited item storage!",
		Icon = "📦",
		Price = 149, -- Robux
		Category = "Convenience",
		Effects = {
			UnlimitedStorage = true
		},
		Permanent = true
	},
	
	AUTO_COLLECT = {
		Id = 123456798, -- Replace with actual gamepass ID
		Name = "Auto Collect",
		Description = "Automatically collect income from all buildings every minute!",
		Icon = "🤖",
		Price = 199, -- Robux
		Category = "Convenience",
		Effects = {
			AutoCollect = true
		},
		Permanent = true
	},
	
	-- Special Editions
	GOLDEN_EDITION = {
		Id = 123456799, -- Replace with actual gamepass ID
		Name = "Golden Edition",
		Description = "Ultimate package: All premium buildings + Double income + VIP + 1M Pieces!",
		Icon = "🏆",
		Price = 999, -- Robux
		Category = "Special",
		Includes = {
			"PREMIUM_BUILDINGS",
			"DOUBLE_INCOME",
			"VIP_STATUS",
			"FAST_BUILD"
		},
		Rewards = {
			Pieces = 1000000,
			Cash = 1000,
			ClesDiamant = 100,
			GammaCoin = 50
		},
		Permanent = true
	}
}

-- Gamepass categories for organization
GamepassSystem.CATEGORIES = {
	Currency = {
		Name = "Currency Packs",
		Icon = "💰",
		Description = "Boost your resources instantly"
	},
	Buildings = {
		Name = "Building Unlocks",
		Icon = "🏗️",
		Description = "Unlock exclusive buildings"
	},
	Multipliers = {
		Name = "Multipliers & Boosts",
		Icon = "📈",
		Description = "Permanent gameplay boosts"
	},
	Convenience = {
		Name = "Convenience Features",
		Icon = "⚡",
		Description = "Quality of life improvements"
	},
	VIP = {
		Name = "VIP Features",
		Icon = "👑",
		Description = "Exclusive VIP benefits"
	},
	Special = {
		Name = "Special Editions",
		Icon = "🏆",
		Description = "Ultimate value packages"
	}
}

-- Check if player owns a gamepass
function GamepassSystem.HasGamepass(player, gamepassKey)
	local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
	if not gamepass then return false end
	
	-- This would normally check with MarketplaceService
	-- For now, return false (implement with actual marketplace check)
	return false
end

-- Get all gamepasses player owns
function GamepassSystem.GetOwnedGamepasses(player)
	local owned = {}
	
	for gamepassKey, gamepass in pairs(GamepassSystem.GAMEPASSES) do
		if GamepassSystem.HasGamepass(player, gamepassKey) then
			owned[gamepassKey] = gamepass
		end
	end
	
	return owned
end

-- Get gamepasses by category - FIXED IMPLEMENTATION
function GamepassSystem.GetGamepassesByCategory(category)
	local categoryPasses = {}

	for gamepassKey, gamepass in pairs(GamepassSystem.GAMEPASSES) do
		if gamepass.Category == category then
			categoryPasses[gamepassKey] = gamepass
		end
	end

	print("🛒 Found", #categoryPasses, "gamepasses for category:", category)
	return categoryPasses
end

-- Calculate total multipliers from owned gamepasses
function GamepassSystem.GetPlayerMultipliers(player)
	local multipliers = {
		Income = 1.0,
		XP = 1.0,
		BuildSpeed = 1.0
	}
	
	for gamepassKey, gamepass in pairs(GamepassSystem.GAMEPASSES) do
		if GamepassSystem.HasGamepass(player, gamepassKey) and gamepass.Effects then
			if gamepass.Effects.IncomeMultiplier then
				multipliers.Income = multipliers.Income * gamepass.Effects.IncomeMultiplier
			end
			if gamepass.Effects.XPMultiplier then
				multipliers.XP = multipliers.XP * gamepass.Effects.XPMultiplier
			end
			if gamepass.Effects.InstantBuild then
				multipliers.BuildSpeed = math.huge -- Instant
			end
		end
	end
	
	return multipliers
end

-- Get player's special effects from gamepasses
function GamepassSystem.GetPlayerEffects(player)
	local effects = {
		VIPStatus = false,
		AutoCollect = false,
		UnlimitedStorage = false,
		InstantBuild = false,
		DailyVIPReward = false,
		ChatTag = nil
	}
	
	for gamepassKey, gamepass in pairs(GamepassSystem.GAMEPASSES) do
		if GamepassSystem.HasGamepass(player, gamepassKey) and gamepass.Effects then
			for effectName, effectValue in pairs(gamepass.Effects) do
				if effects[effectName] ~= nil then
					effects[effectName] = effectValue
				end
			end
		end
	end
	
	return effects
end

-- Get unlocked buildings from gamepasses
function GamepassSystem.GetUnlockedBuildings(player)
	local unlockedBuildings = {}
	
	for gamepassKey, gamepass in pairs(GamepassSystem.GAMEPASSES) do
		if GamepassSystem.HasGamepass(player, gamepassKey) and gamepass.Unlocks then
			for _, buildingType in ipairs(gamepass.Unlocks) do
				unlockedBuildings[buildingType] = true
			end
		end
	end
	
	return unlockedBuildings
end

-- Process gamepass purchase rewards
function GamepassSystem.ProcessGamepassRewards(player, gamepassKey)
	local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
	if not gamepass then return false end
	
	-- Give immediate rewards
	if gamepass.Rewards then
		return gamepass.Rewards
	end
	
	return nil
end

-- Get gamepass purchase URL
function GamepassSystem.GetPurchaseURL(gamepassKey)
	local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
	if not gamepass then return nil end
	
	-- Return marketplace URL for the gamepass
	return "https://www.roblox.com/game-pass/" .. gamepass.Id .. "/purchase"
end

-- Validate gamepass configuration
function GamepassSystem.ValidateGamepass(gamepassKey)
	local gamepass = GamepassSystem.GAMEPASSES[gamepassKey]
	if not gamepass then return false, "Gamepass not found" end
	
	-- Check required fields
	if not gamepass.Id or not gamepass.Name or not gamepass.Price then
		return false, "Missing required fields"
	end
	
	-- Check ID is valid number
	if type(gamepass.Id) ~= "number" or gamepass.Id <= 0 then
		return false, "Invalid gamepass ID"
	end
	
	return true, "Valid gamepass"
end

-- Get recommended gamepasses for player
function GamepassSystem.GetRecommendedGamepasses(player, playerData)
	local recommendations = {}
	
	-- Recommend based on player progress
	if (playerData.Pieces or 0) < 50000 then
		table.insert(recommendations, "STARTER_PACK")
	end
	
	if (playerData.XP or 0) < 1000 then
		table.insert(recommendations, "TRIPLE_XP")
	end
	
	if #(playerData.Buildings or {}) > 10 then
		table.insert(recommendations, "PREMIUM_BUILDINGS")
		table.insert(recommendations, "DOUBLE_INCOME")
	end
	
	if #(playerData.Buildings or {}) > 25 then
		table.insert(recommendations, "VIP_STATUS")
		table.insert(recommendations, "AUTO_COLLECT")
	end
	
	return recommendations
end

return GamepassSystem
