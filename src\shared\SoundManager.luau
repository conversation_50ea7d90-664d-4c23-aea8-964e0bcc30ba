-- SoundManager.luau
-- Comprehensive sound system for UrbanSim

local SoundService = game:GetService("SoundService")
local TweenService = game:GetService("TweenService")
local RunService = game:GetService("RunService")

local SoundManager = {}

-- Sound configuration
local SOUND_CONFIG = {
	-- Master volume settings
	MASTER_VOLUME = 0.5,
	MUSIC_VOLUME = 0.3,
	SFX_VOLUME = 0.7,
	UI_VOLUME = 0.5,
	AMBIENT_VOLUME = 0.4,
	
	-- Fade settings
	FADE_TIME = 2,
	CROSSFADE_TIME = 1.5,
	
	-- Sound categories
	CATEGORIES = {
		MUSIC = "Music",
		SFX = "SoundEffects", 
		UI = "UserInterface",
		AMBIENT = "Ambient",
		VOICE = "Voice"
	}
}

-- Sound library with Roblox audio IDs
local SOUND_LIBRARY = {
	-- Background Music
	MUSIC = {
		MAIN_THEME = {
			Id = "rbxassetid://1837879082", -- Peaceful city theme
			Volume = 0.3,
			Looped = true,
			Category = SOUND_CONFIG.CATEGORIES.MUSIC
		},
		BUILD_THEME = {
			Id = "rbxassetid://1837879082", -- Building/construction theme
			Volume = 0.25,
			Looped = true,
			Category = SOUND_CONFIG.CATEGORIES.MUSIC
		},
		MENU_THEME = {
			Id = "rbxassetid://1837879082", -- Menu/UI theme
			Volume = 0.2,
			Looped = true,
			Category = SOUND_CONFIG.CATEGORIES.MUSIC
		}
	},
	
	-- Sound Effects
	SFX = {
		-- Building sounds
		BUILDING_PLACE = {
			Id = "rbxassetid://131961136", -- Building placement
			Volume = 0.6,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		BUILDING_UPGRADE = {
			Id = "rbxassetid://131961136", -- Building upgrade
			Volume = 0.7,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		BUILDING_DEMOLISH = {
			Id = "rbxassetid://131961136", -- Building demolition
			Volume = 0.8,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		
		-- Resource sounds
		MONEY_EARN = {
			Id = "rbxassetid://131961136", -- Money/cash earned
			Volume = 0.5,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		RESOURCE_COLLECT = {
			Id = "rbxassetid://131961136", -- Resource collection
			Volume = 0.4,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		
		-- Achievement sounds
		LEVEL_UP = {
			Id = "rbxassetid://131961136", -- Level up
			Volume = 0.8,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		ACHIEVEMENT = {
			Id = "rbxassetid://131961136", -- Achievement unlocked
			Volume = 0.7,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		
		-- Error/warning sounds
		ERROR = {
			Id = "rbxassetid://131961136", -- Error notification
			Volume = 0.6,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		},
		WARNING = {
			Id = "rbxassetid://131961136", -- Warning notification
			Volume = 0.5,
			Category = SOUND_CONFIG.CATEGORIES.SFX
		}
	},
	
	-- UI Sounds
	UI = {
		BUTTON_CLICK = {
			Id = "rbxassetid://131961136", -- Button click
			Volume = 0.3,
			Category = SOUND_CONFIG.CATEGORIES.UI
		},
		BUTTON_HOVER = {
			Id = "rbxassetid://131961136", -- Button hover
			Volume = 0.2,
			Category = SOUND_CONFIG.CATEGORIES.UI
		},
		WINDOW_OPEN = {
			Id = "rbxassetid://131961136", -- Window/menu open
			Volume = 0.4,
			Category = SOUND_CONFIG.CATEGORIES.UI
		},
		WINDOW_CLOSE = {
			Id = "rbxassetid://131961136", -- Window/menu close
			Volume = 0.3,
			Category = SOUND_CONFIG.CATEGORIES.UI
		},
		TAB_SWITCH = {
			Id = "rbxassetid://131961136", -- Tab switching
			Volume = 0.25,
			Category = SOUND_CONFIG.CATEGORIES.UI
		},
		NOTIFICATION = {
			Id = "rbxassetid://131961136", -- Notification popup
			Volume = 0.5,
			Category = SOUND_CONFIG.CATEGORIES.UI
		}
	},
	
	-- Ambient Sounds
	AMBIENT = {
		CITY_AMBIENCE = {
			Id = "rbxassetid://1837879082", -- City background ambience
			Volume = 0.2,
			Looped = true,
			Category = SOUND_CONFIG.CATEGORIES.AMBIENT
		},
		CONSTRUCTION = {
			Id = "rbxassetid://1837879082", -- Construction ambience
			Volume = 0.15,
			Looped = true,
			Category = SOUND_CONFIG.CATEGORIES.AMBIENT
		},
		NATURE = {
			Id = "rbxassetid://1837879082", -- Nature/park ambience
			Volume = 0.1,
			Looped = true,
			Category = SOUND_CONFIG.CATEGORIES.AMBIENT
		}
	}
}

-- Active sounds tracking
local activeSounds = {}
local currentMusic = nil
local soundGroups = {}
local masterVolume = SOUND_CONFIG.MASTER_VOLUME
local isMuted = false

-- Initialize sound system
function SoundManager.Initialize()
	print("🔊 Initializing Sound Manager...")
	
	-- Create sound groups for different categories
	for categoryName, _ in pairs(SOUND_CONFIG.CATEGORIES) do
		local soundGroup = Instance.new("SoundGroup")
		soundGroup.Name = categoryName
		soundGroup.Parent = SoundService
		soundGroups[categoryName] = soundGroup
		
		-- Set initial volumes with master volume applied
		local baseVolume = 0.5 -- Default
		if categoryName == SOUND_CONFIG.CATEGORIES.MUSIC then
			baseVolume = SOUND_CONFIG.MUSIC_VOLUME
		elseif categoryName == SOUND_CONFIG.CATEGORIES.SFX then
			baseVolume = SOUND_CONFIG.SFX_VOLUME
		elseif categoryName == SOUND_CONFIG.CATEGORIES.UI then
			baseVolume = SOUND_CONFIG.UI_VOLUME
		elseif categoryName == SOUND_CONFIG.CATEGORIES.AMBIENT then
			baseVolume = SOUND_CONFIG.AMBIENT_VOLUME
		end

		-- Apply master volume multiplier
		soundGroup.Volume = baseVolume * masterVolume
	end
	
	-- Master volume is managed through individual sound groups
	-- No need to set SoundService.MasterVolume as it doesn't exist

	print("✅ Sound Manager initialized with", #soundGroups, "sound groups!")
end

-- Create and configure a sound object
function SoundManager.CreateSound(soundData, parent)
	local sound = Instance.new("Sound")
	sound.SoundId = soundData.Id
	sound.Volume = soundData.Volume or 0.5
	sound.Looped = soundData.Looped or false
	sound.Parent = parent or SoundService
	
	-- Assign to sound group
	if soundData.Category and soundGroups[soundData.Category] then
		sound.SoundGroup = soundGroups[soundData.Category]
	end
	
	return sound
end

-- Play a sound effect
function SoundManager.PlaySFX(soundName, position)
	local soundData = SOUND_LIBRARY.SFX[soundName]
	if not soundData then
		warn("🔊 Sound effect not found:", soundName)
		return nil
	end
	
	local sound = SoundManager.CreateSound(soundData)
	
	-- Add 3D positioning if position provided
	if position then
		local part = Instance.new("Part")
		part.Name = "SoundSource"
		part.Size = Vector3.new(1, 1, 1)
		part.Position = position
		part.Anchored = true
		part.CanCollide = false
		part.Transparency = 1
		part.Parent = workspace
		
		sound.Parent = part
		sound.RollOffMode = Enum.RollOffMode.Linear
		sound.MaxDistance = 100
		
		-- Clean up after sound finishes
		sound.Ended:Connect(function()
			part:Destroy()
		end)
	end
	
	sound:Play()
	
	-- Auto-cleanup for non-looped sounds
	if not sound.Looped then
		sound.Ended:Connect(function()
			sound:Destroy()
		end)
	end
	
	print("🔊 Playing SFX:", soundName)
	return sound
end

-- Play UI sound
function SoundManager.PlayUI(soundName)
	local soundData = SOUND_LIBRARY.UI[soundName]
	if not soundData then
		warn("🔊 UI sound not found:", soundName)
		return nil
	end
	
	local sound = SoundManager.CreateSound(soundData)
	sound:Play()
	
	-- Auto-cleanup
	sound.Ended:Connect(function()
		sound:Destroy()
	end)
	
	return sound
end

-- Play background music with crossfade
function SoundManager.PlayMusic(musicName, fadeIn)
	local soundData = SOUND_LIBRARY.MUSIC[musicName]
	if not soundData then
		warn("🔊 Music not found:", musicName)
		return nil
	end
	
	fadeIn = fadeIn ~= false -- Default to true
	
	-- Stop current music with fade out
	if currentMusic then
		SoundManager.StopMusic(fadeIn)
	end
	
	-- Create new music
	local music = SoundManager.CreateSound(soundData)
	
	if fadeIn then
		music.Volume = 0
		music:Play()
		
		-- Fade in
		TweenService:Create(music, TweenInfo.new(SOUND_CONFIG.FADE_TIME), {
			Volume = soundData.Volume
		}):Play()
	else
		music:Play()
	end
	
	currentMusic = music
	activeSounds[musicName] = music
	
	print("🔊 Playing music:", musicName)
	return music
end

-- Stop background music
function SoundManager.StopMusic(fadeOut)
	if not currentMusic then return end
	
	fadeOut = fadeOut ~= false -- Default to true
	
	if fadeOut then
		-- Fade out then stop
		local fadeOutTween = TweenService:Create(currentMusic, TweenInfo.new(SOUND_CONFIG.FADE_TIME), {
			Volume = 0
		})
		
		fadeOutTween.Completed:Connect(function()
			if currentMusic then
				currentMusic:Stop()
				currentMusic:Destroy()
				currentMusic = nil
			end
		end)
		
		fadeOutTween:Play()
	else
		currentMusic:Stop()
		currentMusic:Destroy()
		currentMusic = nil
	end
	
	print("🔊 Stopping music")
end

-- Play ambient sound
function SoundManager.PlayAmbient(soundName, position)
	local soundData = SOUND_LIBRARY.AMBIENT[soundName]
	if not soundData then
		warn("🔊 Ambient sound not found:", soundName)
		return nil
	end
	
	local sound = SoundManager.CreateSound(soundData)
	
	-- Add 3D positioning if provided
	if position then
		local part = Instance.new("Part")
		part.Name = "AmbientSource"
		part.Size = Vector3.new(1, 1, 1)
		part.Position = position
		part.Anchored = true
		part.CanCollide = false
		part.Transparency = 1
		part.Parent = workspace
		
		sound.Parent = part
		sound.RollOffMode = Enum.RollOffMode.Linear
		sound.MaxDistance = 200
	end
	
	sound:Play()
	activeSounds[soundName] = sound
	
	print("🔊 Playing ambient:", soundName)
	return sound
end

-- Stop ambient sound
function SoundManager.StopAmbient(soundName, fadeOut)
	local sound = activeSounds[soundName]
	if not sound then return end
	
	fadeOut = fadeOut ~= false -- Default to true
	
	if fadeOut then
		local fadeOutTween = TweenService:Create(sound, TweenInfo.new(SOUND_CONFIG.FADE_TIME), {
			Volume = 0
		})
		
		fadeOutTween.Completed:Connect(function()
			sound:Stop()
			sound:Destroy()
			activeSounds[soundName] = nil
		end)
		
		fadeOutTween:Play()
	else
		sound:Stop()
		sound:Destroy()
		activeSounds[soundName] = nil
	end
	
	print("🔊 Stopping ambient:", soundName)
end

-- Set volume for a category
function SoundManager.SetCategoryVolume(category, volume)
	local soundGroup = soundGroups[category]
	if soundGroup then
		volume = math.clamp(volume, 0, 1)

		-- Update the base volume in config
		if category == SOUND_CONFIG.CATEGORIES.MUSIC then
			SOUND_CONFIG.MUSIC_VOLUME = volume
		elseif category == SOUND_CONFIG.CATEGORIES.SFX then
			SOUND_CONFIG.SFX_VOLUME = volume
		elseif category == SOUND_CONFIG.CATEGORIES.UI then
			SOUND_CONFIG.UI_VOLUME = volume
		elseif category == SOUND_CONFIG.CATEGORIES.AMBIENT then
			SOUND_CONFIG.AMBIENT_VOLUME = volume
		end

		-- Apply volume with master volume multiplier
		soundGroup.Volume = volume * masterVolume
		print("🔊 Set", category, "volume to", volume)
	end
end

-- Set master volume by adjusting all sound groups
function SoundManager.SetMasterVolume(volume)
	volume = math.clamp(volume, 0, 1)
	masterVolume = volume
	SOUND_CONFIG.MASTER_VOLUME = volume

	-- Apply master volume to all sound groups
	for categoryName, soundGroup in pairs(soundGroups) do
		local baseVolume = 0.5 -- Default base volume
		if categoryName == SOUND_CONFIG.CATEGORIES.MUSIC then
			baseVolume = SOUND_CONFIG.MUSIC_VOLUME
		elseif categoryName == SOUND_CONFIG.CATEGORIES.SFX then
			baseVolume = SOUND_CONFIG.SFX_VOLUME
		elseif categoryName == SOUND_CONFIG.CATEGORIES.UI then
			baseVolume = SOUND_CONFIG.UI_VOLUME
		elseif categoryName == SOUND_CONFIG.CATEGORIES.AMBIENT then
			baseVolume = SOUND_CONFIG.AMBIENT_VOLUME
		end

		-- Apply master volume multiplier
		soundGroup.Volume = baseVolume * masterVolume
	end

	print("🔊 Set master volume to", volume)
end

-- Get current volumes
function SoundManager.GetVolumes()
	return {
		Master = masterVolume,
		Music = soundGroups[SOUND_CONFIG.CATEGORIES.MUSIC] and soundGroups[SOUND_CONFIG.CATEGORIES.MUSIC].Volume or 0,
		SFX = soundGroups[SOUND_CONFIG.CATEGORIES.SFX] and soundGroups[SOUND_CONFIG.CATEGORIES.SFX].Volume or 0,
		UI = soundGroups[SOUND_CONFIG.CATEGORIES.UI] and soundGroups[SOUND_CONFIG.CATEGORIES.UI].Volume or 0,
		Ambient = soundGroups[SOUND_CONFIG.CATEGORIES.AMBIENT] and soundGroups[SOUND_CONFIG.CATEGORIES.AMBIENT].Volume or 0
	}
end

-- Stop all sounds
function SoundManager.StopAllSounds()
	-- Stop music
	SoundManager.StopMusic(false)
	
	-- Stop all active sounds
	for soundName, sound in pairs(activeSounds) do
		if sound and sound.Parent then
			sound:Stop()
			sound:Destroy()
		end
		activeSounds[soundName] = nil
	end
	
	print("🔊 Stopped all sounds")
end

-- Mute/unmute all sounds
function SoundManager.SetMuted(muted)
	isMuted = muted

	if muted then
		-- Mute all sound groups
		for _, soundGroup in pairs(soundGroups) do
			soundGroup.Volume = 0
		end
	else
		-- Restore volumes using master volume
		SoundManager.SetMasterVolume(masterVolume)
	end

	print("🔊", muted and "Muted" or "Unmuted", "all sounds")
end

return SoundManager
