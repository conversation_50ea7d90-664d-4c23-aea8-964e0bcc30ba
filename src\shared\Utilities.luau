--[[
	Utility Functions for UrbanSim
	Common utility functions used across client and server
]]

local Utilities = {}

-- Format numbers with commas
function Utilities.FormatNumber(number)
	local formatted = tostring(number)
	local k
	while true do
		formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", '%1,%2')
		if k == 0 then
			break
		end
	end
	return formatted
end

-- Calculate distance between two points
function Utilities.Distance(point1, point2)
	return (point1 - point2).Magnitude
end

-- Lerp between two values
function Utilities.Lerp(a, b, t)
	return a + (b - a) * t
end

-- Round number to specified decimal places
function Utilities.Round(number, decimals)
	local mult = 10 ^ (decimals or 0)
	return math.floor(number * mult + 0.5) / mult
end

-- Check if a point is within a rectangle
function Utilities.IsPointInRect(point, rectMin, rectMax)
	return point.X >= rectMin.X and point.X <= rectMax.X and
		   point.Z >= rectMin.Z and point.Z <= rectMax.Z
end

-- Generate a unique ID
function Utilities.GenerateId()
	return tostring(tick()) .. "_" .. tostring(math.random(1000, 9999))
end

-- Deep copy a table
function Utilities.DeepCopy(original)
	local copy = {}
	for key, value in pairs(original) do
		if type(value) == "table" then
			copy[key] = Utilities.DeepCopy(value)
		else
			copy[key] = value
		end
	end
	return copy
end

return Utilities