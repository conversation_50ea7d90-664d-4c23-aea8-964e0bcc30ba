-- BuildingCategories.luau
-- Building categories configuration for UrbanSim
-- Defines how buildings are organized in the UI

local BuildingCategories = {}

-- Building categories with icons, colors, and building lists
BuildingCategories.CATEGORIES = {
	{
		Name = "Residential",
		Icon = "🏠",
		Color = Color3.new(0.8, 0.6, 0.4),
		Description = "Housing for your citizens",
		Buildings = {"HOUSE_SMALL", "HOUSE_MEDIUM", "APARTMENT", "MANSION", "SKYSCRAPER"}
	},
	{
		Name = "Commercial", 
		Icon = "🏢",
		Color = Color3.new(0.2, 0.6, 0.8),
		Description = "Businesses and commerce",
		Buildings = {"SHOP_SMALL", "SHOP_MEDIUM", "SHOP_LARGE", "OFFICE", "MALL", "BANK", "RESTAURANT", "HOTEL", "GAS_STATION"}
	},
	{
		Name = "Industrial",
		Icon = "🏭", 
		Color = Color3.new(0.6, 0.6, 0.6),
		Description = "Production and manufacturing",
		Buildings = {"FACTORY_SMALL", "FACTORY_LARGE", "WAREHOUSE", "METAL_FACTORY", "PLASTIC_FACTORY", "WOOD_FACTORY", "ELECTRONICS_FACTORY", "TECH_FACTORY"}
	},
	{
		Name = "Service",
		Icon = "🚑",
		Color = Color3.new(0.8, 0.8, 0.2),
		Description = "Essential city services",
		Buildings = {"POLICE_STATION", "FIRE_STATION", "HOSPITAL", "SCHOOL", "LIBRARY", "UNIVERSITY"}
	},
	{
		Name = "Utility",
		Icon = "⚡",
		Color = Color3.new(0.5, 0.5, 0.8),
		Description = "Infrastructure and utilities",
		Buildings = {"POWER_PLANT", "WATER_PLANT", "WATER_TREATMENT", "SOLAR_PLANT", "WIND_TURBINE", "GARBAGE_DUMP", "RECYCLING_CENTER", "WASTE_MANAGEMENT"}
	},
	{
		Name = "Decoration",
		Icon = "🌳",
		Color = Color3.new(0.2, 0.8, 0.2),
		Description = "Parks and beautification",
		Buildings = {"PARK_SMALL", "PARK_MEDIUM", "PARK_LARGE", "FOUNTAIN", "STATUE", "PLAYGROUND", "GARDEN", "MONUMENT"}
	},
	{
		Name = "Special",
		Icon = "🏛️",
		Color = Color3.new(0.8, 0.2, 0.8),
		Description = "Unique landmark buildings",
		Buildings = {"CITY_HALL", "AIRPORT", "STADIUM", "LIGHTHOUSE"}
	}
}

-- Helper functions
function BuildingCategories.GetCategoryByName(categoryName)
	for _, category in ipairs(BuildingCategories.CATEGORIES) do
		if category.Name == categoryName then
			return category
		end
	end
	return nil
end

function BuildingCategories.GetCategoryForBuilding(buildingType)
	for _, category in ipairs(BuildingCategories.CATEGORIES) do
		for _, building in ipairs(category.Buildings) do
			if building == buildingType then
				return category
			end
		end
	end
	return nil
end

function BuildingCategories.GetAllBuildings()
	local allBuildings = {}
	for _, category in ipairs(BuildingCategories.CATEGORIES) do
		for _, building in ipairs(category.Buildings) do
			table.insert(allBuildings, building)
		end
	end
	return allBuildings
end

function BuildingCategories.GetCategoryNames()
	local names = {}
	for _, category in ipairs(BuildingCategories.CATEGORIES) do
		table.insert(names, category.Name)
	end
	return names
end

function BuildingCategories.ValidateCategories(configBuildings)
	local missingBuildings = {}
	local foundBuildings = {}
	
	for _, category in ipairs(BuildingCategories.CATEGORIES) do
		for _, buildingType in ipairs(category.Buildings) do
			if configBuildings[buildingType] then
				table.insert(foundBuildings, buildingType)
			else
				table.insert(missingBuildings, buildingType)
			end
		end
	end
	
	return {
		missing = missingBuildings,
		found = foundBuildings,
		totalCategories = #BuildingCategories.CATEGORIES,
		totalBuildings = #BuildingCategories.GetAllBuildings()
	}
end

return BuildingCategories
